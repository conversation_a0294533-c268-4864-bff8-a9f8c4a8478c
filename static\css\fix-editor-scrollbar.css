/* 
 * Fix for double scrollbars in the document editor
 * This file provides specific fixes for the scrollbar issue in different editor components
 */

/* Main fix: Restore scrolling within the editor */
.document-editor-content {
    overflow-y: auto !important; /* Allow vertical scrolling within the editor */
    max-height: 500px !important; /* Set maximum height for the editor area */
    min-height: 450px; /* Keep minimum height for editor */
}

/* Fix for the CKEditor content area */
.ck.ck-editor__editable_inline,
.ck.ck-editor__editable,
.ck.ck-content {
    overflow-y: auto !important; /* Restore internal scrollbar */
    max-height: 500px !important; /* Limit height to create scrollbar */
    min-height: 450px; /* Keep minimum height for editor */
}

/* Fix for any CKEditor related containers */
.ck-editor__editable {
    overflow-y: auto !important;
    max-height: 500px !important;
}

/* Fix for the document editor editable area */
.document-editor__editable {
    overflow-y: auto !important;
    max-height: 500px !important;
}

/* Advanced editor wrapper - ensure proper containment */
.advanced-editor-wrapper {
    position: relative;
    height: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* Make sure CKEditor content has scrollbar */
.ck-content {
    overflow-y: auto !important;
    max-height: 500px !important;
}

/* Ensure the outer editor container allows internal scrolling */
#document-editor {
    overflow: visible !important;
}

/* Make the main container's height fixed */
#advanced-converter.tab-content {
    height: auto !important;
    overflow: visible !important;
}

/* Control the main page scrollbar only */
html, body {
    overflow-x: hidden; /* Prevent horizontal scrollbar */
}

html {
    overflow-y: scroll !important; /* Always show main scrollbar on html element */
}

body {
    overflow-y: visible !important; /* Let body content flow naturally */
}

/* Fix for mobile view */
@media (max-width: 768px) {
    .document-editor-content {
        overflow-y: auto !important;
        max-height: 450px !important;
    }
    
    .ck-content,
    .ck-editor__editable,
    .document-editor__editable,
    .ck.ck-editor__editable_inline {
        overflow-y: auto !important;
        max-height: 450px !important;
    }
    
    #document-editor,
    .advanced-editor-wrapper {
        overflow: visible !important;
    }
}
