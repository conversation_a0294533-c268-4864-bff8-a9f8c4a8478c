/**
 * JavaScript to fix scrollbar appearance
 * Makes sure the editor has its internal scrollbar while maintaining the page scrollbar
 */

(function() {
    // Execute when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get the main HTML element
        const html = document.documentElement;
        // Ensure main page scrollbar is always visible
        html.style.overflowY = 'scroll';
        
        // Fix function that will be called multiple times
        function fixEditorScrollbars() {
            // Get all editor elements that should have scrollbars
            const editorElements = document.querySelectorAll('.document-editor-content, .ck-editor__editable, .ck-content');
            
            // Apply scrollbar styles to each element
            editorElements.forEach(function(element) {
                if (element) {
                    element.style.overflowY = 'auto';
                    element.style.maxHeight = '500px';
                }
            });
            
            // Make sure outer containers don't block internal scrolling
            const outerElements = document.querySelectorAll('.advanced-editor-wrapper, #document-editor');
            outerElements.forEach(function(element) {
                if (element) {
                    element.style.overflow = 'visible';
                }
            });
        }
        
        // Run fix initially
        fixEditorScrollbars();
        
        // Also run when tab is clicked
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                // Wait a bit for the tab content to be visible
                setTimeout(fixEditorScrollbars, 100);
            });
        });
        
        // Run fix when window is resized
        window.addEventListener('resize', fixEditorScrollbars);
        
        // Make function available globally
        window.fixEditorScrollbars = fixEditorScrollbars;
    });
})();
