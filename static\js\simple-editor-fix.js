/**
 * إصلاح مشاكل المحرر البسيط
 * - ضمان ظهور شريط التمرير
 * - إصلاح تبديل اللغة
 */

document.addEventListener('DOMContentLoaded', function() {
    // إصلاح شريط التمرير في المحرر البسيط
    function fixSimpleEditorScrollbar() {
        const latinText = document.getElementById('latin-text');
        const tifinagh = document.getElementById('tifinagh-text');
        
        if (latinText) {
            // ضمان ظهور شريط التمرير
            latinText.style.overflowY = 'auto';
            latinText.style.overflowX = 'hidden';
            latinText.style.scrollbarWidth = 'auto'; // Firefox
            latinText.style.msOverflowStyle = 'auto'; // IE
            
            // إزالة أي إعدادات قد تخفي شريط التمرير
            latinText.style.display = 'block';
            latinText.style.visibility = 'visible';
            latinText.style.opacity = '1';
            
            console.log('تم إصلاح شريط التمرير للنص اللاتيني');
        }
        
        if (tifinagh) {
            // ضمان ظهور شريط التمرير
            tifinagh.style.overflowY = 'auto';
            tifinagh.style.overflowX = 'hidden';
            tifinagh.style.scrollbarWidth = 'auto'; // Firefox
            tifinagh.style.msOverflowStyle = 'auto'; // IE
            
            // إزالة أي إعدادات قد تخفي شريط التمرير
            tifinagh.style.display = 'block';
            tifinagh.style.visibility = 'visible';
            tifinagh.style.opacity = '1';
            
            console.log('تم إصلاح شريط التمرير للنص التيفيناغي');
        }
        
        // إصلاح إضافي لجميع عناصر المحرر البسيط
        const textAreas = document.querySelectorAll('.text-area textarea, .text-area .tifinagh-text');
        textAreas.forEach(element => {
            element.style.overflowY = 'auto';
            element.style.overflowX = 'hidden';
            element.style.scrollbarWidth = 'auto';
            element.style.msOverflowStyle = 'auto';
            element.style.display = 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
        });
    }
    
    // إصلاح تبديل اللغة
    function fixLanguageSwitching() {
        // انتظار تحميل نظام الترجمة
        function waitForI18n() {
            if (window.i18n && window.i18n.isLoaded) {
                setupLanguageButtons();
            } else if (window.i18n) {
                window.i18n.onLoad(setupLanguageButtons);
            } else {
                // إعادة المحاولة بعد 100ms
                setTimeout(waitForI18n, 100);
            }
        }
        
        function setupLanguageButtons() {
            const languageButtons = document.querySelectorAll('.lang-btn');
            
            languageButtons.forEach(button => {
                // إزالة مستمعي الأحداث السابقين
                button.removeEventListener('click', handleLanguageClick);
                
                // إضافة مستمع الأحداث الجديد
                button.addEventListener('click', handleLanguageClick);
            });
            
            console.log('تم إعداد أزرار اللغة:', languageButtons.length);
        }
        
        function handleLanguageClick(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const button = e.currentTarget;
            const lang = button.getAttribute('data-lang');
            
            console.log('تم النقر على زر اللغة:', lang);
            
            if (window.i18n && lang) {
                // تغيير اللغة
                window.i18n.setLanguage(lang);
                
                // تحديث حالة الأزرار
                updateLanguageButtonsState(lang);
                
                // تحديث النص في زر القائمة المنسدلة
                updateDropdownButtonText(button);
                
                // حفظ اختيار المستخدم
                localStorage.setItem('selectedLanguage', lang);
                localStorage.setItem('preferred_language', lang);
                
                console.log('تم تغيير اللغة إلى:', lang);
            } else {
                console.error('نظام الترجمة غير متاح أو اللغة غير محددة');
            }
        }
        
        function updateLanguageButtonsState(lang) {
            const languageButtons = document.querySelectorAll('.lang-btn');
            
            languageButtons.forEach(btn => {
                // إزالة الفئة النشطة من جميع الأزرار
                btn.classList.remove('active');
                
                // إظهار أو إخفاء أيقونة التحقق
                const checkIcon = btn.querySelector('.bi-check2');
                if (checkIcon) {
                    if (btn.getAttribute('data-lang') === lang) {
                        checkIcon.classList.remove('invisible');
                        btn.classList.add('active');
                    } else {
                        checkIcon.classList.add('invisible');
                    }
                }
            });
        }
        
        function updateDropdownButtonText(button) {
            const currentLanguageDisplay = document.querySelector('.current-language');
            if (currentLanguageDisplay && button) {
                const spanElement = button.querySelector('span:last-child');
                if (spanElement) {
                    const clonedSpan = spanElement.cloneNode(true);
                    currentLanguageDisplay.innerHTML = '';
                    currentLanguageDisplay.appendChild(clonedSpan);
                }
            }
        }
        
        // بدء عملية انتظار نظام الترجمة
        waitForI18n();
    }
    
    // تطبيق الإصلاحات
    fixSimpleEditorScrollbar();
    fixLanguageSwitching();
    
    // إعادة تطبيق إصلاح شريط التمرير عند تغيير التبويب
    document.addEventListener('shown.bs.tab', function(e) {
        if (e.target.getAttribute('data-bs-target') === '#simple-converter') {
            setTimeout(fixSimpleEditorScrollbar, 100);
        }
    });
    
    // إعادة تطبيق الإصلاحات عند تغيير حجم النافذة
    window.addEventListener('resize', function() {
        setTimeout(fixSimpleEditorScrollbar, 100);
    });
    
    // إعادة تطبيق الإصلاحات عند تحديث المحتوى
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const simpleConverter = document.getElementById('simple-converter');
                if (simpleConverter && simpleConverter.classList.contains('active')) {
                    setTimeout(fixSimpleEditorScrollbar, 100);
                }
            }
        });
    });
    
    // مراقبة التغييرات في DOM
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('تم تحميل إصلاحات المحرر البسيط');
});
