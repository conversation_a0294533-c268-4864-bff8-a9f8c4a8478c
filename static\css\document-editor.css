/**
 * Document Editor styles for Advanced Text Converter
 */

/* Main container for the document editor */
.advanced-editor-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin: 0 auto;
    padding: 0;
    box-sizing: border-box;
}

/* Document editor container */
#document-editor {
    display: flex;
    flex-direction: column;
    width: 100%;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Toolbar container */
#document-editor-toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Content area */
.document-editor-content {
    min-height: 450px;
    max-height: 500px; /* زيادة الارتفاع الأقصى إلى 500 بكسل */
    overflow-y: auto; /* السماح بالتمرير العمودي داخل المحرر */
    padding: 1.5rem;
    background-color: #fff;
    font-family: '<PERSON>o', 'Noto Sans Tifinagh', 'Noto Sans Arabic', Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    transition: max-height 0.3s ease; /* انتقال سلس عند تغيير الارتفاع */
}

/* Font faces for multilingual support - using Google Fonts */
/* Noto Sans Arabic and Noto Sans Tifinagh are loaded from Google Fonts in base.html */

/* Editable content area */
.document-editor__editable {
    min-height: 450px;
    max-height: 500px; /* زيادة الارتفاع الأقصى إلى 500 بكسل */
    padding: 1.5rem 2.5rem;
    border: none;
    box-shadow: none;
    background-color: #fff;
    overflow: visible !important; /* منع شريط التمرير المزدوج - استخدام !important للتأكد من تطبيق هذه الخاصية */
}

/* تحسين تجربة المستخدم عند التمرير - إضافة انتقال سلس لارتفاع المحرر */
/* نلاحظ: تم دمج الخصائص مع تعريف document-editor-content الأساسي */

/* إضافة مؤشر للإشارة إلى وجود محتوى إضافي */
.document-editor-content::after {
    content: '';
    display: block;
    /* height: 10px; */
    /* margin-top: 10px; */
    background: linear-gradient(to bottom, rgba(0,0,0,0.05), transparent);
    border-radius: 0 0 4px 4px;
    pointer-events: none;
}

/* Text area footer */
.text-area-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-light);
}

.character-count {
    color: var(--text-light);
    font-size: 0.875rem;
}

.text-actions {
    display: flex;
    gap: 0.5rem;
}



/* Clear, Copy, and Convert buttons */
.clear-btn, .copy-btn, .convert-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    cursor: pointer;
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.clear-btn:hover, .copy-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb, 79, 70, 229), 0.05);
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.clear-btn:active, .copy-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

/* Convert button */
.convert-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: auto !important;
    height: auto !important;
    padding: 0.5rem 1rem !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.convert-btn span {
    display: inline-block;
    white-space: nowrap;
}

.convert-btn:hover {
    background-color: var(--primary-dark, #4338ca);
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
}

.convert-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

/* Copy button success state */
.copy-btn.success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #28a745;
}

/* Spinning animation for loading state */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.spin {
    animation: spin 1.5s linear infinite;
}

/* Convert button disabled state */
.convert-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
    opacity: 0.7;
}

/* Table styles */
.ck-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1.5rem 0;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.ck-content table td,
.ck-content table th {
    border: 1px solid #e2e8f0;
    padding: 12px 16px;
    vertical-align: middle;
}

.ck-content table th {
    padding-top: 14px;
    padding-bottom: 14px;
    text-align: left;
    background-color: #f8fafc;
    font-weight: 600;
    color: #334155;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ck-content table tr:nth-child(even) {
    background-color: #f8fafc;
}

.ck-content table tr:hover {
    background-color: #f1f5f9;
}

/* Table caption */
.ck-content table figcaption {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #64748b;
    text-align: center;
    font-style: italic;
}

/* Responsive styles */
@media (max-width: 768px) {
    .document-editor-content {
        min-height: 400px;
        max-height: 500px; /* ارتفاع للشاشات المتوسطة */
        padding: 1rem;
        overflow-y: visible; /* نفس الخاصية للتناسق */
    }

    .document-editor__editable {
        min-height: 400px;
        max-height: 500px; /* ارتفاع للشاشات المتوسطة */
        padding: 1rem;
    }

    #document-editor-toolbar {
        padding: 0.5rem;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .editor-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .editor-actions-left {
        width: 100%;
        justify-content: center;
    }

    .convert-btn-container {
        width: 100%;
    }

    .convert-btn {
        width: 100%;
    }

    .ck-content table td,
    .ck-content table th {
        padding: 8px 12px;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .document-editor-content {
        min-height: 350px;
        max-height: 450px; /* ارتفاع أقصر للشاشات الصغيرة */
        padding: 0.75rem;
        overflow-y: visible; /* نفس الخاصية للتناسق */
    }

    .document-editor__editable {
        min-height: 350px;
        max-height: 450px; /* ارتفاع أقصر للشاشات الصغيرة */
        padding: 0.75rem;
    }

    .action-btn {
        width: 36px;
        height: 36px;
    }

    .action-btn svg {
        width: 16px;
        height: 16px;
    }
}
