/**
 * Clean Language Switcher Implementation
 * Built from scratch for reliable language switching functionality
 */

class LanguageSwitcher {
    constructor() {
        this.isInitialized = false;
        this.debug = true; // Enable debugging
        this.retryCount = 0;
        this.maxRetries = 30; // 3 seconds max wait
        
        this.log('🌐 Language Switcher initialized');
        this.init();
    }
    
    log(message, data = null) {
        if (this.debug) {
            console.log(`[LanguageSwitcher] ${message}`, data || '');
        }
    }
    
    error(message, error = null) {
        console.error(`[LanguageSwitcher] ❌ ${message}`, error || '');
    }
    
    /**
     * Initialize the language switcher
     */
    init() {
        this.log('Starting initialization...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.waitForTranslationSystem());
        } else {
            this.waitForTranslationSystem();
        }
    }
    
    /**
     * Wait for translation system to be ready
     */
    waitForTranslationSystem() {
        this.retryCount++;
        this.log(`Waiting for translation system... (attempt ${this.retryCount})`);
        
        if (this.retryCount > this.maxRetries) {
            this.error('Max retries reached. Translation system not available.');
            return;
        }
        
        // Check if translation system is ready
        if (window.i18n && window.i18n.isLoaded && typeof window.i18n.setLanguage === 'function') {
            this.log('✅ Translation system is ready');
            this.setupLanguageButtons();
        } else {
            // Retry after 100ms
            setTimeout(() => this.waitForTranslationSystem(), 100);
        }
    }
    
    /**
     * Setup language buttons event handlers
     */
    setupLanguageButtons() {
        if (this.isInitialized) {
            this.log('Already initialized, skipping...');
            return;
        }
        
        this.log('Setting up language buttons...');
        
        // Find all language buttons
        const languageButtons = document.querySelectorAll('.lang-btn');
        this.log(`Found ${languageButtons.length} language buttons`);
        
        if (languageButtons.length === 0) {
            this.error('No language buttons found');
            return;
        }
        
        // Add event listeners to each button
        languageButtons.forEach((button, index) => {
            const lang = button.getAttribute('data-lang');
            this.log(`Setting up button ${index + 1}: ${lang}`);
            
            // Remove any existing event listeners by cloning
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // Add click event listener
            newButton.addEventListener('click', (e) => this.handleLanguageClick(e, lang));
        });
        
        // Load saved language preference
        this.loadSavedLanguage();
        
        // Update button states for current language
        this.updateButtonStates(window.i18n.currentLang);
        
        this.isInitialized = true;
        this.log('✅ Language switcher setup complete');
    }
    
    /**
     * Handle language button click
     */
    handleLanguageClick(event, lang) {
        event.preventDefault();
        event.stopPropagation();
        
        this.log(`Language button clicked: ${lang}`);
        
        if (!lang) {
            this.error('No language specified');
            return;
        }
        
        if (!window.i18n || !window.i18n.setLanguage) {
            this.error('Translation system not available');
            return;
        }
        
        try {
            // Change language
            const oldLang = window.i18n.currentLang;
            window.i18n.setLanguage(lang);
            
            this.log(`Language changed from ${oldLang} to ${lang}`);
            
            // Update button states
            this.updateButtonStates(lang);
            
            // Update dropdown text
            this.updateDropdownText(event.target);
            
            // Save preference
            this.saveLanguagePreference(lang);
            
            this.log('✅ Language switch completed successfully');
            
        } catch (error) {
            this.error('Failed to switch language', error);
        }
    }
    
    /**
     * Update button states to reflect current language
     */
    updateButtonStates(currentLang) {
        this.log(`Updating button states for language: ${currentLang}`);
        
        const languageButtons = document.querySelectorAll('.lang-btn');
        
        languageButtons.forEach(button => {
            const buttonLang = button.getAttribute('data-lang');
            const checkIcon = button.querySelector('.bi-check2');
            
            // Remove active state from all buttons
            button.classList.remove('active');
            button.removeAttribute('aria-current');
            
            if (checkIcon) {
                if (buttonLang === currentLang) {
                    // Show check icon for current language
                    checkIcon.classList.remove('invisible');
                    button.classList.add('active');
                    button.setAttribute('aria-current', 'true');
                    this.log(`Activated button for language: ${buttonLang}`);
                } else {
                    // Hide check icon for other languages
                    checkIcon.classList.add('invisible');
                }
            }
        });
    }
    
    /**
     * Update dropdown button text
     */
    updateDropdownText(clickedButton) {
        const currentLanguageDisplay = document.querySelector('.current-language');
        
        if (!currentLanguageDisplay) {
            this.log('Current language display element not found');
            return;
        }
        
        // Find the text span in the clicked button
        const textSpan = clickedButton.querySelector('span:last-child') || 
                        clickedButton.closest('.lang-btn').querySelector('span:last-child');
        
        if (textSpan) {
            // Clone the span to preserve all attributes and content
            const clonedSpan = textSpan.cloneNode(true);
            currentLanguageDisplay.innerHTML = '';
            currentLanguageDisplay.appendChild(clonedSpan);
            
            this.log('Updated dropdown text');
        } else {
            this.log('Text span not found in clicked button');
        }
    }
    
    /**
     * Save language preference to localStorage
     */
    saveLanguagePreference(lang) {
        try {
            localStorage.setItem('selectedLanguage', lang);
            localStorage.setItem('preferred_language', lang);
            this.log(`Saved language preference: ${lang}`);
        } catch (error) {
            this.error('Failed to save language preference', error);
        }
    }
    
    /**
     * Load saved language preference
     */
    loadSavedLanguage() {
        try {
            const savedLang = localStorage.getItem('selectedLanguage') || 
                             localStorage.getItem('preferred_language');
            
            if (savedLang && savedLang !== window.i18n.currentLang) {
                this.log(`Loading saved language: ${savedLang}`);
                window.i18n.setLanguage(savedLang);
                this.updateButtonStates(savedLang);
                
                // Update dropdown text for saved language
                const savedLangButton = document.querySelector(`.lang-btn[data-lang="${savedLang}"]`);
                if (savedLangButton) {
                    this.updateDropdownText(savedLangButton);
                }
            } else if (savedLang) {
                this.log(`Current language matches saved language: ${savedLang}`);
            } else {
                this.log('No saved language preference found');
            }
        } catch (error) {
            this.error('Failed to load saved language', error);
        }
    }
    
    /**
     * Public method to manually switch language (for testing)
     */
    switchLanguage(lang) {
        this.log(`Manual language switch requested: ${lang}`);
        
        if (!window.i18n || !window.i18n.setLanguage) {
            this.error('Translation system not available for manual switch');
            return false;
        }
        
        try {
            window.i18n.setLanguage(lang);
            this.updateButtonStates(lang);
            this.saveLanguagePreference(lang);
            
            // Update dropdown text
            const langButton = document.querySelector(`.lang-btn[data-lang="${lang}"]`);
            if (langButton) {
                this.updateDropdownText(langButton);
            }
            
            this.log(`✅ Manual language switch to ${lang} completed`);
            return true;
        } catch (error) {
            this.error('Manual language switch failed', error);
            return false;
        }
    }
    
    /**
     * Get current language
     */
    getCurrentLanguage() {
        return window.i18n ? window.i18n.currentLang : null;
    }
    
    /**
     * Check if system is ready
     */
    isReady() {
        return this.isInitialized && window.i18n && window.i18n.isLoaded;
    }
}

// Initialize the language switcher
const languageSwitcher = new LanguageSwitcher();

// Expose for testing and debugging
window.languageSwitcher = languageSwitcher;

// Expose convenient test functions
window.switchToEnglish = () => languageSwitcher.switchLanguage('en');
window.switchToAmazigh = () => languageSwitcher.switchLanguage('am');
window.getCurrentLang = () => languageSwitcher.getCurrentLanguage();

console.log('🌐 Language Switcher module loaded');
