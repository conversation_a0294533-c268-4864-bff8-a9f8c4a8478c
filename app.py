import os
import time
import threading
import logging
import json
import shutil
from datetime import datetime, timedelta

# استيراد مكتبات إضافية
try:
    import requests
    from bs4 import BeautifulSoup
    REQUESTS_AVAILABLE = True
except ImportError:
    # إذا لم تكن المكتبات مثبتة، سنستمر بدونها
    # وسنتعامل مع الأخطاء عند استخدامها
    REQUESTS_AVAILABLE = False
    # إنشاء بديل وهمي لـ requests
    class DummyRequests:
        def get(self, *args, **kwargs):
            raise ImportError("مكتبة requests غير متوفرة")
    
    requests = DummyRequests()

# استيراد المكتبات بعد التأكد من تثبيتها
from flask import Flask, render_template, request, jsonify, redirect, url_for, send_file, Response, session, make_response
# من أجل الإصدارات المستقبلية
# from flask_wtf.csrf import CSRFProtect, generate_csrf

# استيراد مسارات لوحة التحكم
from admin_routes import admin_bp

# استيراد مدير الترجمة الجديد
import i18n_manager
# تعريف دالة secure_filename للاستخدام في التطبيق
def secure_filename(filename):
    """
    تأمين اسم الملف لمنع هجمات path traversal وضمان أسماء ملفات آمنة

    Args:
        filename (str): اسم الملف المراد تأمينه

    Returns:
        str: اسم الملف المؤمن
    """
    if not filename:
        return ""

    # إزالة المسارات المطلقة
    filename = os.path.basename(filename)

    # استبدال الأحرف غير الآمنة بالشرطة السفلية
    # الأحرف المسموح بها: الأحرف الأبجدية الرقمية، النقطة، الشرطة السفلية، الشرطة
    safe_chars = set("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789._-")

    # استبدال الأحرف غير الآمنة
    filename = ''.join(c if c in safe_chars else '_' for c in filename)

    # التأكد من أن الملف لا يبدأ بنقطة (لتجنب الملفات المخفية)
    if filename.startswith('.'):
        filename = '_' + filename[1:]

    # التأكد من أن الملف ليس فارغًا
    if not filename:
        filename = "unnamed_file"

    return filename

from config import get_config
from utils.converter import latin_to_tifinagh
from utils.file_handler import process_file
from create_docx import create_docx, convert_existing_docx, check_required_libraries

# إعداد التسجيل
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# إعداد التطبيق
app = Flask(__name__)
app.config.from_object(get_config())

# إعداد مفتاح سري للجلسات
app.secret_key = os.environ.get('SECRET_KEY', 'tifinagh-secret-key-2024')
# تعيين مدة انتهاء الجلسة (30 دقيقة)
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=30)

# تسجيل مسارات لوحة التحكم
app.register_blueprint(admin_bp)

# تهيئة مدير الترجمة الجديد
# لا يتطلب تهيئة خاصة لمدير الترجمة

# وظيفة لتحميل إحصائيات التطبيق
def load_stats():
    """
    تحميل إحصائيات التطبيق من ملف JSON.
    إذا لم يكن الملف موجودًا، يتم إنشاء ملف جديد بالإحصائيات الافتراضية.

    Returns:
        dict: قاموس يحتوي على إحصائيات التطبيق
    """
    stats_file = os.path.join(os.path.dirname(__file__), 'data', 'stats.json')

    # الإحصائيات الافتراضية
    default_stats = {
        'text_conversions': 0,
        'file_conversions': 0,
        'web_conversions': 0,
        'unique_visitors': 0,
        'recent_activities': [],
        'daily_stats': {},
        'monthly_stats': {},
        'hourly_stats': {},  # إحصائيات ساعية
        'browser_stats': {},  # إحصائيات المتصفحات
        'os_stats': {},  # إحصائيات أنظمة التشغيل
        'device_stats': {},  # إحصائيات الأجهزة
        'conversion_times': [],  # أوقات التحويل للتحليل
        'error_stats': {},  # إحصائيات الأخطاء
        'performance_metrics': {  # مقاييس الأداء
            'avg_conversion_time_ms': 0,
            'max_conversion_time_ms': 0,
            'min_conversion_time_ms': 0,
            'total_conversions': 0
        },
        'last_cleanup': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # وقت آخر تنظيف
        'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # وقت آخر تحديث
    }

    try:
        # محاولة قراءة الملف
        if os.path.exists(stats_file):
            with open(stats_file, 'r', encoding='utf-8') as f:
                loaded_stats = json.load(f)

                # دمج الإحصائيات المحملة مع الإحصائيات الافتراضية لضمان وجود جميع المفاتيح
                # هذا يضمن أن الإحصائيات الجديدة ستكون موجودة حتى في الملفات القديمة
                merged_stats = default_stats.copy()
                merged_stats.update(loaded_stats)

                # تنظيف الإحصائيات القديمة
                merged_stats = _cleanup_old_stats(merged_stats)

                return merged_stats
        else:
            # إنشاء ملف جديد بالإحصائيات الافتراضية
            os.makedirs(os.path.dirname(stats_file), exist_ok=True)
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(default_stats, f, ensure_ascii=False, indent=4)
            return default_stats
    except FileNotFoundError as e:
        logger.error(f"ملف الإحصائيات غير موجود: {str(e)}")
        return default_stats
    except json.JSONDecodeError as e:
        logger.error(f"خطأ في تنسيق ملف الإحصائيات JSON: {str(e)}")
        # في حالة وجود خطأ في تنسيق الملف، قم بإنشاء نسخة احتياطية من الملف الحالي
        if os.path.exists(stats_file):
            backup_file = f"{stats_file}.backup.{datetime.now().strftime('%Y%m%d%H%M%S')}"
            try:
                shutil.copy2(stats_file, backup_file)
                logger.info(f"تم إنشاء نسخة احتياطية من ملف الإحصائيات: {backup_file}")
            except Exception as backup_error:
                logger.error(f"فشل إنشاء نسخة احتياطية من ملف الإحصائيات: {str(backup_error)}")
        return default_stats
    except PermissionError as e:
        logger.error(f"خطأ في صلاحيات الوصول لملف الإحصائيات: {str(e)}")
        return default_stats
    except IOError as e:
        logger.error(f"خطأ في قراءة/كتابة ملف الإحصائيات: {str(e)}")
        return default_stats
    except Exception as e:
        logger.error(f"خطأ غير متوقع في تحميل الإحصائيات: {str(e)}")
        return default_stats

def _cleanup_old_stats(stats_dict):
    """
    تنظيف الإحصائيات القديمة للحفاظ على حجم ملف الإحصائيات.

    Args:
        stats_dict (dict): قاموس الإحصائيات

    Returns:
        dict: قاموس الإحصائيات بعد التنظيف
    """
    try:
        # التحقق من وقت آخر تنظيف
        last_cleanup = datetime.strptime(stats_dict.get('last_cleanup', '2000-01-01 00:00:00'), '%Y-%m-%d %H:%M:%S')
        now = datetime.now()

        # تنظيف الإحصائيات مرة واحدة في اليوم
        if (now - last_cleanup).days < 1:
            return stats_dict

        # تحديث وقت آخر تنظيف
        stats_dict['last_cleanup'] = now.strftime('%Y-%m-%d %H:%M:%S')

        # الاحتفاظ بإحصائيات الأيام الـ 30 الأخيرة فقط
        if 'daily_stats' in stats_dict:
            daily_stats = stats_dict['daily_stats']
            cutoff_date = (now - timedelta(days=30)).strftime('%Y-%m-%d')

            # حذف الإحصائيات القديمة
            stats_dict['daily_stats'] = {date: stats for date, stats in daily_stats.items() if date >= cutoff_date}

        # الاحتفاظ بإحصائيات الأشهر الـ 12 الأخيرة فقط
        if 'monthly_stats' in stats_dict:
            monthly_stats = stats_dict['monthly_stats']
            cutoff_month = (now - timedelta(days=365)).strftime('%Y-%m')

            # حذف الإحصائيات القديمة
            stats_dict['monthly_stats'] = {month: stats for month, stats in monthly_stats.items() if month >= cutoff_month}

        # الاحتفاظ بآخر 100 نشاط فقط
        if 'recent_activities' in stats_dict:
            stats_dict['recent_activities'] = stats_dict['recent_activities'][-100:] if len(stats_dict['recent_activities']) > 100 else stats_dict['recent_activities']

        # الاحتفاظ بآخر 1000 وقت تحويل فقط
        if 'conversion_times' in stats_dict:
            stats_dict['conversion_times'] = stats_dict['conversion_times'][-1000:] if len(stats_dict['conversion_times']) > 1000 else stats_dict['conversion_times']

        logger.info("تم تنظيف الإحصائيات القديمة")
        return stats_dict
    except Exception as e:
        logger.error(f"خطأ أثناء تنظيف الإحصائيات القديمة: {str(e)}")
        return stats_dict

# وظيفة لحفظ إحصائيات التطبيق
def save_stats(stats_dict):
    """
    حفظ إحصائيات التطبيق في ملف JSON.
    """
    stats_file = os.path.join(os.path.dirname(__file__), 'data', 'stats.json')

    try:
        # التأكد من وجود مجلد البيانات
        os.makedirs(os.path.dirname(stats_file), exist_ok=True)

        # تحديث وقت آخر تحديث
        stats_dict['last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # حفظ الإحصائيات في الملف
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats_dict, f, ensure_ascii=False, indent=4)

        logger.debug("تم حفظ الإحصائيات بنجاح")
        return True
    except FileNotFoundError as e:
        logger.error(f"مسار ملف الإحصائيات غير صالح: {str(e)}")
        return False
    except PermissionError as e:
        logger.error(f"خطأ في صلاحيات الوصول لملف الإحصائيات: {str(e)}")
        return False
    except IOError as e:
        logger.error(f"خطأ في كتابة ملف الإحصائيات: {str(e)}")
        return False
    except TypeError as e:
        logger.error(f"خطأ في نوع البيانات المراد حفظها: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"خطأ غير متوقع في حفظ الإحصائيات: {str(e)}")
        return False

# وظيفة لتحديث الإحصائيات
def update_stats(stat_type, increment=1, additional_data=None):
    """
    تحديث إحصائيات التطبيق.

    Args:
        stat_type (str): نوع الإحصائية ('text', 'file', 'web', 'visitor', 'error')
        increment (int): قيمة الزيادة (افتراض 1)
        additional_data (dict, optional): بيانات إضافية للإحصائيات مثل وقت التحويل، نوع المتصفح، إلخ.
    """
    global app_stats

    try:
        # التأكد من وجود البنية الأساسية للإحصائيات
        if 'daily_stats' not in app_stats:
            app_stats['daily_stats'] = {}

        if 'monthly_stats' not in app_stats:
            app_stats['monthly_stats'] = {}

        if 'hourly_stats' not in app_stats:
            app_stats['hourly_stats'] = {}

        if 'recent_activities' not in app_stats:
            app_stats['recent_activities'] = []

        # الحصول على التاريخ والوقت الحاليين
        now = datetime.now()
        today = now.strftime('%Y-%m-%d')
        month = now.strftime('%Y-%m')
        hour = now.strftime('%Y-%m-%d %H')

        # تحديث الإحصائيات العامة
        if stat_type == 'text':
            app_stats['text_conversions'] += increment
        elif stat_type == 'file':
            app_stats['file_conversions'] += increment
        elif stat_type == 'web':
            app_stats['web_conversions'] += increment
        elif stat_type == 'visitor':
            app_stats['unique_visitors'] += increment
        elif stat_type == 'error':
            # تحديث إحصائيات الأخطاء
            if 'error_stats' not in app_stats:
                app_stats['error_stats'] = {}

            error_type = additional_data.get('error_type', 'unknown') if additional_data else 'unknown'

            if error_type not in app_stats['error_stats']:
                app_stats['error_stats'][error_type] = 0

            app_stats['error_stats'][error_type] += increment

        # تحديث الإحصائيات اليومية
        if today not in app_stats['daily_stats']:
            app_stats['daily_stats'][today] = {
                'text_conversions': 0,
                'file_conversions': 0,
                'web_conversions': 0,
                'unique_visitors': 0
            }

        if stat_type == 'text':
            app_stats['daily_stats'][today]['text_conversions'] += increment
        elif stat_type == 'file':
            app_stats['daily_stats'][today]['file_conversions'] += increment
        elif stat_type == 'web':
            app_stats['daily_stats'][today]['web_conversions'] += increment
        elif stat_type == 'visitor':
            app_stats['daily_stats'][today]['unique_visitors'] += increment

        # تحديث الإحصائيات الشهرية
        if month not in app_stats['monthly_stats']:
            app_stats['monthly_stats'][month] = {
                'text_conversions': 0,
                'file_conversions': 0,
                'web_conversions': 0,
                'unique_visitors': 0
            }

        if stat_type == 'text':
            app_stats['monthly_stats'][month]['text_conversions'] += increment
        elif stat_type == 'file':
            app_stats['monthly_stats'][month]['file_conversions'] += increment
        elif stat_type == 'web':
            app_stats['monthly_stats'][month]['web_conversions'] += increment
        elif stat_type == 'visitor':
            app_stats['monthly_stats'][month]['unique_visitors'] += increment

        # تحديث الإحصائيات الساعية
        if hour not in app_stats['hourly_stats']:
            app_stats['hourly_stats'][hour] = {
                'text_conversions': 0,
                'file_conversions': 0,
                'web_conversions': 0,
                'unique_visitors': 0
            }

        if stat_type == 'text':
            app_stats['hourly_stats'][hour]['text_conversions'] += increment
        elif stat_type == 'file':
            app_stats['hourly_stats'][hour]['file_conversions'] += increment
        elif stat_type == 'web':
            app_stats['hourly_stats'][hour]['web_conversions'] += increment
        elif stat_type == 'visitor':
            app_stats['hourly_stats'][hour]['unique_visitors'] += increment

        # تحديث إحصائيات المتصفحات وأنظمة التشغيل والأجهزة
        if additional_data:
            # تحديث إحصائيات المتصفحات
            browser = additional_data.get('browser')
            if browser and stat_type != 'error':
                if 'browser_stats' not in app_stats:
                    app_stats['browser_stats'] = {}

                if browser not in app_stats['browser_stats']:
                    app_stats['browser_stats'][browser] = 0

                app_stats['browser_stats'][browser] += increment

            # تحديث إحصائيات أنظمة التشغيل
            os_name = additional_data.get('os')
            if os_name and stat_type != 'error':
                if 'os_stats' not in app_stats:
                    app_stats['os_stats'] = {}

                if os_name not in app_stats['os_stats']:
                    app_stats['os_stats'][os_name] = 0

                app_stats['os_stats'][os_name] += increment

            # تحديث إحصائيات الأجهزة
            device = additional_data.get('device')
            if device and stat_type != 'error':
                if 'device_stats' not in app_stats:
                    app_stats['device_stats'] = {}

                if device not in app_stats['device_stats']:
                    app_stats['device_stats'][device] = 0

                app_stats['device_stats'][device] += increment

            # تحديث أوقات التحويل
            conversion_time = additional_data.get('conversion_time_ms')
            if conversion_time is not None and stat_type in ['text', 'file', 'web']:
                if 'conversion_times' not in app_stats:
                    app_stats['conversion_times'] = []

                # إضافة وقت التحويل إلى القائمة
                app_stats['conversion_times'].append(conversion_time)

                # تحديث مقاييس الأداء
                if 'performance_metrics' not in app_stats:
                    app_stats['performance_metrics'] = {
                        'avg_conversion_time_ms': 0,
                        'max_conversion_time_ms': 0,
                        'min_conversion_time_ms': 0,
                        'total_conversions': 0
                    }

                # حساب المتوسط الجديد
                total_conversions = app_stats['performance_metrics']['total_conversions'] + 1
                current_avg = app_stats['performance_metrics']['avg_conversion_time_ms']
                new_avg = ((current_avg * (total_conversions - 1)) + conversion_time) / total_conversions

                # تحديث المقاييس
                app_stats['performance_metrics']['avg_conversion_time_ms'] = new_avg
                app_stats['performance_metrics']['max_conversion_time_ms'] = max(app_stats['performance_metrics']['max_conversion_time_ms'], conversion_time)

                # تحديث الحد الأدنى إذا كان هذا أول تحويل أو إذا كان أقل من الحد الأدنى الحالي
                if app_stats['performance_metrics']['min_conversion_time_ms'] == 0 or conversion_time < app_stats['performance_metrics']['min_conversion_time_ms']:
                    app_stats['performance_metrics']['min_conversion_time_ms'] = conversion_time

                app_stats['performance_metrics']['total_conversions'] = total_conversions

        # إضافة نشاط جديد إلى قائمة الأنشطة الحديثة
        if stat_type in ['text', 'file', 'web']:
            activity = {
                'type': stat_type,
                'timestamp': now.strftime('%Y-%m-%d %H:%M:%S'),
                'details': additional_data if additional_data else {}
            }

            app_stats['recent_activities'].append(activity)

            # الاحتفاظ بآخر 100 نشاط فقط
            if len(app_stats['recent_activities']) > 100:
                app_stats['recent_activities'] = app_stats['recent_activities'][-100:]

        # تحديث وقت آخر تحديث
        app_stats['last_updated'] = now.strftime('%Y-%m-%d %H:%M:%S')

        # حفظ الإحصائيات
        save_stats(app_stats)
    except Exception as e:
        logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")
        # لا نريد أن يؤثر هذا الخطأ على تجربة المستخدم

# تحميل إحصائيات التطبيق عند بدء التطبيق
app_stats = load_stats()

# وظيفة لتحميل إعدادات التطبيق
def load_settings():
    """
    تحميل إعدادات التطبيق من ملف JSON.
    إذا لم يكن الملف موجودًا، يتم إنشاء ملف جديد بالإعدادات الافتراضية.

    Returns:
        dict: قاموس يحتوي على إعدادات التطبيق
    """
    settings_file = os.path.join(os.path.dirname(__file__), 'data', 'settings.json')

    # الإعدادات الافتراضية
    default_settings = {
        'bilingual_mode': True,  # تفعيل وضع ثنائي اللغة افتراض
        'primary_color': '#0d6efd',  # اللون الأساسي
        'secondary_color': '#6c757d',  # اللون الثانوي
        'show_logo': True,  # إظهار الشعار
        'maintenance_mode': False,  # وضع الصيانة
        'maintenance_message': 'الموقع قيد الصيانة حال. يرجى المحاولة لاحق.',  # رسالة الصيانة
        'max_file_size_mb': 10,  # الحد الأقصى لحجم الملف بالميجابايت
        'enable_file_conversion': True,  # تفعيل تحويل الملفات
        'enable_web_conversion': True,  # تفعيل تحويل مواقع الويب
        'enable_text_conversion': True,  # تفعيل تحويل النصوص
        'default_language': 'en',  # اللغة الافتراضية
        'available_languages': ['en', 'am'],  # اللغات المتاحة (الإنجليزية والأمازيغية)
        'auto_cleanup_temp_files': True,  # تنظيف الملفات المؤقتة تلقائيًا
        'cleanup_interval_minutes': 30,  # فترة تنظيف الملفات المؤقتة بالدقائق
        'temp_file_max_age_hours': 1,  # العمر الأقصى للملفات المؤقتة بالساعات
        'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # وقت آخر تحديث
    }

    try:
        # محاولة قراءة الملف
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                loaded_settings = json.load(f)

                # دمج الإعدادات المحملة مع الإعدادات الافتراضية لضمان وجود جميع المفاتيح
                # هذا يضمن أن الإعدادات الجديدة ستكون موجودة حتى في الملفات القديمة
                merged_settings = default_settings.copy()
                merged_settings.update(loaded_settings)

                # تحديث وقت آخر تحميل
                merged_settings['last_loaded'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                return merged_settings
        else:
            # إنشاء ملف جديد بالإعدادات الافتراضية
            os.makedirs(os.path.dirname(settings_file), exist_ok=True)
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(default_settings, f, ensure_ascii=False, indent=4)
            return default_settings
    except FileNotFoundError as e:
        logger.error(f"ملف الإعدادات غير موجود: {str(e)}")
        return default_settings
    except json.JSONDecodeError as e:
        logger.error(f"خطأ في تنسيق ملف الإعدادات JSON: {str(e)}")
        # في حالة وجود خطأ في تنسيق الملف، قم بإنشاء نسخة احتياطية من الملف الحالي
        if os.path.exists(settings_file):
            backup_file = f"{settings_file}.backup.{datetime.now().strftime('%Y%m%d%H%M%S')}"
            try:
                shutil.copy2(settings_file, backup_file)
                logger.info(f"تم إنشاء نسخة احتياطية من ملف الإعدادات: {backup_file}")
            except Exception as backup_error:
                logger.error(f"فشل إنشاء نسخة احتياطية من ملف الإعدادات: {str(backup_error)}")
        return default_settings
    except PermissionError as e:
        logger.error(f"خطأ في صلاحيات الوصول لملف الإعدادات: {str(e)}")
        return default_settings
    except IOError as e:
        logger.error(f"خطأ في قراءة/كتابة ملف الإعدادات: {str(e)}")
        return default_settings
    except Exception as e:
        logger.error(f"خطأ غير متوقع في تحميل الإعدادات: {str(e)}")
        return default_settings

# وظيفة لحفظ إعدادات التطبيق
def save_settings(settings_dict):
    """
    حفظ إعدادات التطبيق في ملف JSON.
    """
    settings_file = os.path.join(os.path.dirname(__file__), 'data', 'settings.json')

    try:
        # التأكد من وجود مجلد البيانات
        os.makedirs(os.path.dirname(settings_file), exist_ok=True)

        # حفظ الإعدادات في الملف
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings_dict, f, ensure_ascii=False, indent=4)

        logger.info("تم حفظ الإعدادات بنجاح")
        return True
    except FileNotFoundError as e:
        logger.error(f"مسار ملف الإعدادات غير صالح: {str(e)}")
        return False
    except PermissionError as e:
        logger.error(f"خطأ في صلاحيات الوصول لملف الإعدادات: {str(e)}")
        return False
    except IOError as e:
        logger.error(f"خطأ في كتابة ملف الإعدادات: {str(e)}")
        return False
    except TypeError as e:
        logger.error(f"خطأ في نوع البيانات المراد حفظها: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"خطأ غير متوقع في حفظ الإعدادات: {str(e)}")
        return False

# تحميل إعدادات التطبيق عند بدء التطبيق
app_settings = load_settings()

# وظيفة لتحميل سياسات التطبيق
def load_policies():
    """
    تحميل سياسات التطبيق من ملف JSON.
    إذا لم يكن الملف موجودًا، يتم إنشاء ملف جديد بالسياسات الافتراضية.
    """
    policies_file = os.path.join(os.path.dirname(__file__), 'data', 'policies.json')

    # السياسات الافتراضية
    default_policies = {
        'privacy_enabled': False,
        'privacy_title': 'سياسة الخصوصية',
        'privacy_content': get_default_privacy_policy(),
        'terms_enabled': False,
        'terms_title': 'شروط الاستخدام',
        'terms_content': get_default_terms(),
        'cookie_enabled': False,
        'cookie_title': 'سياسة ملفات تعريف الارتباط',
        'cookie_content': get_default_cookie_policy(),
        'show_consent_banner': False,
        'consent_title': 'موافقة ملفات تعريف الارتباط',
        'consent_message': 'يستخدم هذا الموقع ملفات تعريف الارتباط لضمان حصولك على أفضل تجربة على موقعنا.',
        'accept_button_text': 'قبول الكل',
        'reject_button_text': 'رفض الكل',
        'settings_button_text': 'إعدادات ملفات تعريف الارتباط',
        'banner_position': 'bottom',
        'banner_theme': 'light'
    }

    try:
        # محاولة قراءة الملف
        if os.path.exists(policies_file):
            with open(policies_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # إنشاء ملف جديد بالسياسات الافتراضية
            os.makedirs(os.path.dirname(policies_file), exist_ok=True)
            with open(policies_file, 'w', encoding='utf-8') as f:
                json.dump(default_policies, f, ensure_ascii=False, indent=4)
            return default_policies
    except FileNotFoundError as e:
        logger.error(f"ملف السياسات غير موجود: {str(e)}")
        return default_policies
    except json.JSONDecodeError as e:
        logger.error(f"خطأ في تنسيق ملف السياسات JSON: {str(e)}")
        return default_policies
    except PermissionError as e:
        logger.error(f"خطأ في صلاحيات الوصول لملف السياسات: {str(e)}")
        return default_policies
    except IOError as e:
        logger.error(f"خطأ في قراءة/كتابة ملف السياسات: {str(e)}")
        return default_policies
    except Exception as e:
        logger.error(f"خطأ غير متوقع في تحميل السياسات: {str(e)}")
        return default_policies

# وظيفة لحفظ سياسات التطبيق
def save_policies(policies_dict):
    """
    حفظ سياسات التطبيق في ملف JSON.
    """
    policies_file = os.path.join(os.path.dirname(__file__), 'data', 'policies.json')

    try:
        # التأكد من وجود مجلد البيانات
        os.makedirs(os.path.dirname(policies_file), exist_ok=True)

        # حفظ السياسات في الملف
        with open(policies_file, 'w', encoding='utf-8') as f:
            json.dump(policies_dict, f, ensure_ascii=False, indent=4)

        logger.info("تم حفظ السياسات بنجاح")
        return True
    except FileNotFoundError as e:
        logger.error(f"مسار ملف السياسات غير صالح: {str(e)}")
        return False
    except PermissionError as e:
        logger.error(f"خطأ في صلاحيات الوصول لملف السياسات: {str(e)}")
        return False
    except IOError as e:
        logger.error(f"خطأ في كتابة ملف السياسات: {str(e)}")
        return False
    except TypeError as e:
        logger.error(f"خطأ في نوع البيانات المراد حفظها: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"خطأ غير متوقع في حفظ السياسات: {str(e)}")
        return False

# وظائف للحصول على النصوص الافتراضية للسياسات
def get_default_privacy_policy():
    """
    الحصول على النص الافتراضي لسياسة الخصوصية.
    """
    return """
<h2>سياسة الخصوصية</h2>

<p>نحن نقدر خصوصيتك ونلتزم بحمايتها. تشرح سياسة الخصوصية هذه كيفية جمعنا واستخدامنا وحمايتنا لمعلوماتك عند استخدام موقعنا.</p>

<h3>المعلومات التي نجمعها</h3>

<p>نحن لا نجمع أي معلومات شخصية عنك عند استخدام موقعنا. جميع عمليات التحويل تتم محليًا في متصفحك ولا يتم إرسال أي بيانات إلى خوادمنا.</p>

<h3>ملفات تعريف الارتباط</h3>

<p>نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا. يمكنك اختيار قبول أو رفض ملفات تعريف الارتباط.</p>

<h3>التغييرات على سياسة الخصوصية</h3>

<p>قد نقوم بتحديث سياسة الخصوصية الخاصة بنا من وقت لآخر. سنقوم بنشر أي تغييرات على هذه الصفحة.</p>
"""

def get_default_terms():
    """
    الحصول على النص الافتراضي لشروط الاستخدام.
    """
    return """
<h2>شروط الاستخدام</h2>

<p>يرجى قراءة شروط الاستخدام هذه بعناية قبل استخدام موقعنا.</p>

<h3>قبول الشروط</h3>

<p>باستخدام موقعنا، فإنك توافق على الالتزام بهذه الشروط. إذا كنت لا توافق على أي جزء من هذه الشروط، فيرجى عدم استخدام موقعنا.</p>

<h3>الملكية الفكرية</h3>

<p>جميع المحتويات والمواد المتاحة على موقعنا هي ملكية لنا وتخضع لحقوق الطبع والنشر والعلامات التجارية وغيرها من قوانين الملكية الفكرية.</p>

<h3>تقييد المسؤولية</h3>

<p>لن نكون مسؤولين عن أي خسائر أو أضرار ناتجة عن استخدام موقعنا.</p>

<h3>التغييرات على الشروط</h3>

<p>نحتفظ بالحق في تعديل هذه الشروط في أي وقت. ستكون التغييرات سارية المفعول فور نشرها على موقعنا.</p>
"""

def get_default_cookie_policy():
    """
    الحصول على النص الافتراضي لسياسة ملفات تعريف الارتباط.
    """
    return """
<h2>سياسة ملفات تعريف الارتباط</h2>

<p>تشرح هذه السياسة كيفية استخدامنا لملفات تعريف الارتباط على موقعنا.</p>

<h3>ما هي ملفات تعريف الارتباط؟</h3>

<p>ملفات تعريف الارتباط هي ملفات نصية صغيرة يتم تخزينها على جهازك عند زيارة موقعنا. تساعدنا هذه الملفات على تحسين تجربتك على موقعنا.</p>

<h3>أنواع ملفات تعريف الارتباط التي نستخدمها</h3>

<p>نحن نستخدم ملفات تعريف الارتباط الضرورية فقط لضمان عمل موقعنا بشكل صحيح. لا نستخدم ملفات تعريف الارتباط للتتبع أو الإعلانات.</p>

<h3>كيفية إدارة ملفات تعريف الارتباط</h3>

<p>يمكنك تعديل إعدادات متصفحك لرفض جميع ملفات تعريف الارتباط أو لإخطارك عند إرسال ملف تعريف ارتباط. ومع ذلك، قد لا تعمل بعض أجزاء موقعنا بشكل صحيح إذا قمت بتعطيل ملفات تعريف الارتباط.</p>
"""

# Removed get_all_texts_from_app function

# تسجيل دالة الترجمة في قوالب Jinja2
@app.context_processor
def utility_processor():
    return dict(translate=i18n_manager.translate)

# حقن الإعدادات والسياسات في جميع القوالب
@app.context_processor
def inject_settings():
    """تحميل الإعدادات وحقنها في جميع القوالب"""
    settings = load_settings()
    policies = load_policies()
    now = datetime.now()
    return {
        'settings': settings,
        'policies': policies,
        'now': now
    }

# تحسين إعدادات التخزين المؤقت للملفات الثابتة
# التخزين المؤقت للملفات الثابتة التي لا تتغير كثيرًا مثل CSS/JS/الصور
from datetime import timedelta

# إعدادات التخزين المؤقت للملفات الثابتة
# ملفات لا تتغير كثيرًا (CSS, JS, الصور)
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = int(timedelta(days=7).total_seconds())

# تكوين ضغط الاستجابة لتقليل حجم البيانات المرسلة
app.config['COMPRESS_MIMETYPES'] = [
    'text/html', 'text/css', 'text/xml', 'application/json',
    'application/javascript', 'text/javascript'
]
app.config['COMPRESS_LEVEL'] = 6  # مستوى ضغط متوسط (1-9)
app.config['COMPRESS_MIN_SIZE'] = 500  # الحد الأدنى لحجم الملف للضغط (بالبايت)

# وسيط للتحقق من وضع الصيانة
@app.before_request
def check_maintenance():
    """
    التحقق من وضع الصيانة قبل معالجة الطلب.
    إذا كان وضع الصيانة مفعلاً، يتم عرض صفحة الصيانة للمستخدمين العاديين.
    """
    # تجاهل مسارات الإدارة والأصول الثابتة
    if request.path.startswith('/admin') or request.path.startswith('/static') or request.path == '/favicon.ico':
        return None

    settings = load_settings()
    if settings.get('maintenance_mode', False):
        return render_template('maintenance.html',
                              message=settings.get('maintenance_message', 'الموقع قيد الصيانة حال. يرجى المحاولة لاحق.'))

# وسيط لتحسين معالجة الأخطاء
@app.errorhandler(404)
def page_not_found(error):
    """
    معالجة خطأ 404 (الصفحة غير موجودة).
    """
    logger.warning(f"محاولة الوصول إلى صفحة غير موجودة: {request.path} - {str(error)}")
    return render_template('error.html',
                          error="الصفحة غير موجودة",
                          details="الصفحة التي تبحث عنها غير موجودة أو تم نقلها.",
                          code=404), 404

@app.errorhandler(500)
def internal_server_error(error):
    """
    معالجة خطأ 500 (خطأ داخلي في الخادم).
    """
    logger.error(f"خطأ داخلي في الخادم: {str(error)}")
    return render_template('error.html',
                          error="خطأ داخلي في الخادم",
                          details="حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقًا.",
                          code=500), 500

@app.errorhandler(413)
def request_entity_too_large(error):
    """
    معالجة خطأ 413 (حجم الطلب كبير جدًا).
    """
    logger.warning(f"حجم الطلب كبير جدًا: {request.path} - {str(error)}")
    return render_template('error.html',
                          error="حجم الملف كبير جدًا",
                          details="الملف الذي تحاول تحميله كبير جدًا. الحد الأقصى المسموح به هو 10 ميجابايت.",
                          code=413), 413

@app.errorhandler(400)
def bad_request(error):
    """
    معالجة خطأ 400 (طلب غير صالح).
    """
    logger.warning(f"طلب غير صالح: {request.path} - {str(error)}")
    return render_template('error.html',
                          error="طلب غير صالح",
                          details="تعذر معالجة الطلب بسبب بنية غير صالحة أو معلمات مفقودة.",
                          code=400), 400

@app.errorhandler(403)
def forbidden(error):
    """
    معالجة خطأ 403 (محظور).
    """
    logger.warning(f"محاولة الوصول إلى مورد محظور: {request.path} - {str(error)}")
    return render_template('error.html',
                          error="غير مصرح",
                          details="ليس لديك صلاحية للوصول إلى هذا المورد.",
                          code=403), 403
# تكوين أنماط التخزين المؤقت المخصصة لأنواع ملفات محددة
@app.after_request
def add_cache_headers(response):
    """
    إضافة رؤوس التخزين المؤقت المناسبة للاستجابات.

    يتم تعطيل التخزين المؤقت للصفحات الديناميكية ونقاط نهاية API.
    يتم تمكين التخزين المؤقت طويل المدى للأصول الثابتة مثل CSS وJS والصور والخطوط.
    """
    # التحقق من نوع الطلب والمسار
    path = request.path

    try:
        # تعطيل التخزين المؤقت للصفحات الديناميكية ونقاط نهاية API
        if (path.startswith('/api/') or
            request.method != 'GET' or
            path == '/' or
            path.startswith('/admin') or
            path in ['/simple-converter', '/complete-converter', '/web-converter']):
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"

        # التخزين المؤقت للملفات الثابتة
        elif path.startswith('/static/'):
            # إضافة رأس ETag للتحقق من التغييرات
            if not response.headers.get('ETag'):
                response.add_etag()

            # تحديد مدة التخزين المؤقت بناءً على نوع الملف
            if path.endswith(('.css', '.js')):
                # ملفات CSS و JS - أسبوع واحد
                response.headers["Cache-Control"] = "public, max-age=604800, stale-while-revalidate=86400"
            elif path.endswith(('.jpg', '.jpeg', '.png', '.gif', '.ico', '.svg', '.webp')):
                # الصور - شهر واحد
                response.headers["Cache-Control"] = "public, max-age=2592000, stale-while-revalidate=86400"
            elif path.endswith(('.woff', '.woff2', '.ttf', '.eot')):
                # الخطوط - سنة واحدة
                response.headers["Cache-Control"] = "public, max-age=31536000, immutable"
            else:
                # ملفات ثابتة أخرى - يوم واحد
                response.headers["Cache-Control"] = "public, max-age=86400"

            # إضافة رأس Vary للتعامل مع الضغط
            response.headers["Vary"] = "Accept-Encoding"

        # إضافة رؤوس أمان للجميع
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "SAMEORIGIN"
        response.headers["X-XSS-Protection"] = "1; mode=block"

    except Exception as e:
        # تسجيل الخطأ ولكن السماح للاستجابة بالمرور
        logger.error(f"خطأ في إضافة رؤوس التخزين المؤقت: {str(e)}")

    return response

# Create upload folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Create docs folder if it doesn't exist
os.makedirs('docs', exist_ok=True)

# قائمة لتخزين مسارات الملفات المؤقتة
temp_files = []

def cleanup_temp_files():
    """
    تنظيف الملفات المؤقتة التي تم إنشاؤها منذ أكثر من ساعة.
    يعمل على مسح جميع الملفات المؤقتة في مجلد التحميل التي مر عليها وقت طويل.
    """
    global temp_files
    current_time = time.time()
    files_to_remove = []

    try:
        # 1. تنظيف الملفات المتتبعة في القائمة
        for file_path in temp_files:
            try:
                # التحقق من عمر الملف
                if os.path.exists(file_path):
                    file_age = current_time - os.path.getctime(file_path)
                    # إذا كان عمر الملف أكثر من ساعة (3600 ثانية)، قم بحذفه
                    if file_age > 3600:
                        try:
                            # التحقق من أن الملف غير مستخدم حال بقى محاولة حذفه
                            try:
                                # محاولة فتح الملف للكتابة للتحقق من أنه غير مستخدم
                                with open(file_path, 'a', encoding='utf-8') as f:
                                    pass
                                # إذا نجح الفتح، يمكن حذف الملف
                                os.remove(file_path)
                                logger.info(f"تم حذف الملف المؤقت: {file_path}")
                                files_to_remove.append(file_path)
                            except (PermissionError, OSError) as e:
                                if "being used by another process" in str(e) or "used by another process" in str(e):
                                    logger.debug(f"الملف {file_path} قيد الاستخدام حال، سيتم تجاهله")
                                else:
                                    logger.warning(f"تعذر حذف الملف المؤقت {file_path}: {str(e)}")
                        except Exception as e:
                            logger.warning(f"خطأ أثناء محاولة حذف الملف المؤقت {file_path}: {str(e)}")
                else:
                    # إذا كان الملف غير موجود، قم بإزالته من القائمة
                    logger.info(f"الملف المؤقت غير موجود، تتم إزالته من القائمة: {file_path}")
                    files_to_remove.append(file_path)
            except Exception as e:
                logger.error(f"خطأ أثناء تنظيف الملف المؤقت {file_path}: {str(e)}")
                # لا نضيف الملف إلى قائمة الحذف إذا حدث خطأ غير متوقع للمحاولة مرة أخرى لاحق

        # إزالة المسارات من القائمة
        for file_path in files_to_remove:
            try:
                temp_files.remove(file_path)
            except ValueError:
                # في حالة حذف المسار بالفعل من القائمة بطريقة أخرى
                pass

        # 2. مسح إضافي لمجلد التحميل (يتجاهل الملفات المستخدمة)
        upload_folder = app.config['UPLOAD_FOLDER']
        if os.path.exists(upload_folder):
            logger.info(f"بدء التنظيف الشامل لمجلد التحميل: {upload_folder}")
            for filename in os.listdir(upload_folder):
                file_path = os.path.join(upload_folder, filename)
                try:
                    if os.path.isfile(file_path):
                        file_age = current_time - os.path.getctime(file_path)
                        # الملفات الأقدم من ساعتين (7200 ثانية)
                        if file_age > 7200:
                            try:
                                os.remove(file_path)
                                logger.info(f"تم حذف الملف القديم من مجلد التحميل: {file_path}")
                            except (PermissionError, OSError) as e:
                                if "being used by another process" in str(e) or "used by another process" in str(e):
                                    logger.debug(f"الملف {file_path} قيد الاستخدام حاليًا، سيتم تجاهله")
                                else:
                                    logger.warning(f"تعذر حذف الملف القديم {file_path}: {str(e)}")
                except Exception as e:
                    logger.error(f"خطأ أثناء فحص الملف {file_path}: {str(e)}")
    except Exception as e:
        logger.error(f"خطأ غير متوقع أثناء تنظيف الملفات المؤقتة: {str(e)}")

    # إضافة سجل لملخص التنظيف
    logger.info(f"اكتمل تنظيف الملفات المؤقتة. تمت إزالة {len(files_to_remove)} ملف من القائمة. المتبقي: {len(temp_files)}")

def start_cleanup_scheduler():
    """
    بدء جدولة تنظيف الملفات المؤقتة.
    يتم تنفيذ عملية التنظيف بشكل دوري وعند بدء التطبيق.
    """
    def run_cleanup():
        while True:
            try:
                logger.info("بدء دورة تنظيف الملفات المؤقتة")
                cleanup_temp_files()

                # تنظيف كامل للمجلدات المؤقتة كل 24 ساعة
                try:
                    import tempfile
                    temp_dir = tempfile.gettempdir()
                    logger.info(f"تنظيف المجلد المؤقت للنظام: {temp_dir}")
                    # لا نقوم بالتنظيف الكامل، بل نبحث فقط عن ملفات تبدأ بـ "tifinagh_" وقديمة
                    for filename in os.listdir(temp_dir):
                        if filename.startswith("tifinagh_") and os.path.isfile(os.path.join(temp_dir, filename)):
                            file_path = os.path.join(temp_dir, filename)
                            file_age = time.time() - os.path.getctime(file_path)
                            if file_age > 86400:  # ملفات أقدم من يوم كامل
                                try:
                                    os.remove(file_path)
                                    logger.info(f"تم حذف الملف القديم من المجلد المؤقت للنظام: {file_path}")
                                except Exception as e:
                                    logger.debug(f"تعذر حذف الملف المؤقت للنظام {file_path}: {str(e)}")
                except Exception as e:
                    logger.error(f"خطأ أثناء تنظيف المجلد المؤقت للنظام: {str(e)}")

                # انتظار قبل التنظيف التالي (30 دقيقة)
                time.sleep(1800)
            except Exception as e:
                logger.error(f"خطأ في دورة تنظيف الملفات المؤقتة: {str(e)}")
                # في حالة حدوث خطأ، ننتظر فترة قصيرة ثم نحاول مرة أخرى
                time.sleep(300)  # انتظار 5 دقائق

    # تنفيذ عملية تنظيف أولية عند بدء التطبيق
    try:
        cleanup_temp_files()
    except Exception as e:
        logger.error(f"فشل التنظيف الأولي للملفات المؤقتة: {str(e)}")

    # بدء خيط جديد للتنظيف الدوري
    cleanup_thread = threading.Thread(target=run_cleanup, daemon=True)
    cleanup_thread.start()
    logger.info("تم بدء جدولة تنظيف الملفات المؤقتة")

# بدء جدولة التنظيف عند بدء التطبيق
start_cleanup_scheduler()

def allowed_file(filename):
    """
    التحقق مما إذا كان امتداد الملف مسموحًا به بطريقة آمنة.

    Args:
        filename (str): اسم الملف للتحقق منه

    Returns:
        bool: True إذا كان امتداد الملف مسموحًا به، False خلاف ذلك
    """
    if not filename or not isinstance(filename, str):
        return False

    # تنظيف اسم الملف من أي مسارات
    filename = os.path.basename(filename)

    # التحقق من وجود امتداد
    if '.' not in filename:
        return False

    # استخراج الامتداد بطريقة آمنة
    try:
        extension = filename.rsplit('.', 1)[1].lower()
    except IndexError:
        return False

    # التحقق من أن الامتداد في قائمة الامتدادات المسموح بها
    allowed_extensions = app.config.get('ALLOWED_EXTENSIONS', set())
    if not allowed_extensions:
        logger.warning("قائمة الامتدادات المسموح بها فارغة في إعدادات التطبيق")
        return False

    return extension in allowed_extensions

@app.route('/')
def index():
    """
    الصفحة الرئيسية للتطبيق.
    تعرض واجهة موحدة تحتوي على جميع أدوات التحويل.
    """
    # تحديث إحصائيات الزائرين الفريدين بطريقة أكثر أمانًا
    try:
        # استخدام مزيج من عنوان IP ومعلومات المتصفح لإنشاء معرف فريد
        user_agent = request.headers.get('User-Agent', '')
        visitor_id = f"{request.remote_addr}_{hash(user_agent)}"

        # تشفير المعرف قبل تخزينه في الجلسة
        import hashlib
        hashed_visitor_id = hashlib.sha256(visitor_id.encode()).hexdigest()

        # التحقق من وجود معرف الزائر في الجلسة
        if 'visitor_id' not in session:
            # تعيين معرف الزائر في الجلسة
            session['visitor_id'] = hashed_visitor_id
            # تعيين وقت انتهاء صلاحية الجلسة (30 يومًا)
            session.permanent = True
            app.permanent_session_lifetime = timedelta(days=30)
            # تحديث إحصائيات الزائرين الفريدين
            update_stats('visitor')
    except Exception as e:
        # في حالة حدوث خطأ، تسجيل الخطأ والاستمرار
        logger.error(f"خطأ في تحديث إحصائيات الزائرين: {str(e)}")
        # لا نريد أن يؤثر هذا الخطأ على تجربة المستخدم

    tab = request.args.get('tab')
    if tab:
        # Pass the tab parameter to the template for proper initialization
        response = make_response(render_template('unified.html', active_tab=tab))
    else:
        response = make_response(render_template('index.html'))

    # تعطيل التخزين المؤقت للصفحة الرئيسية
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"

    return response

@app.route('/translator')
def translator():
    """
    إعادة توجيه إلى أداة تحويل النص في الواجهة الموحدة.
    """
    return redirect(url_for('index', tab='text-converter'))

@app.route('/translator/files')
def translator_files():
    """
    إعادة توجيه إلى أداة تحويل الملفات في الواجهة الموحدة.
    """
    return redirect(url_for('index', tab='file-converter'))

@app.route('/web-converter')
def web_converter():
    """
    إعادة توجيه إلى أداة تحويل الويب في الواجهة الموحدة.
    """
    return redirect(url_for('index', tab='web-converter'))

@app.route('/about')
def about():
    """
    صفحة معلومات عن التطبيق.
    """
    return render_template('about.html')

@app.route('/privacy-policy')
def privacy_policy():
    """
    صفحة سياسة الخصوصية.
    """
    # تحميل سياسات التطبيق
    policies_dict = load_policies()

    # التحقق مما إذا كانت سياسة الخصوصية مفعلة
    if not policies_dict.get('privacy_enabled', False):
        return render_template('error.html', error="سياسة الخصوصية غير متاحة حاليًا.")

    return render_template('privacy_policy.html',
                          title=policies_dict.get('privacy_title', 'سياسة الخصوصية'),
                          content=policies_dict.get('privacy_content', ''))

@app.route('/terms-of-use')
def terms_of_use():
    """
    صفحة شروط الاستخدام.
    """
    # تحميل سياسات التطبيق
    policies_dict = load_policies()

    # التحقق مما إذا كانت شروط الاستخدام مفعلة
    if not policies_dict.get('terms_enabled', False):
        return render_template('error.html', error="شروط الاستخدام غير متاحة حاليًا.")

    return render_template('terms.html',
                          title=policies_dict.get('terms_title', 'شروط الاستخدام'),
                          content=policies_dict.get('terms_content', ''))

@app.route('/cookie-policy')
def cookie_policy():
    """
    صفحة سياسة ملفات تعريف الارتباط.
    """
    # تحميل سياسات التطبيق
    policies_dict = load_policies()

    # التحقق مما إذا كانت سياسة ملفات تعريف الارتباط مفعلة
    if not policies_dict.get('cookie_enabled', False):
        return render_template('error.html', error="سياسة ملفات تعريف الارتباط غير متاحة حاليًا.")

    return render_template('cookie_policy.html',
                          title=policies_dict.get('cookie_title', 'سياسة ملفات تعريف الارتباط'),
                          content=policies_dict.get('cookie_content', ''))

@app.route('/tab-demo')
def tab_demo():
    """
    صفحة عرض توضيحي لنظام التبويب الموحد.
    """
    return render_template('tab-demo.html')

# واجهة برمجة التطبيقات للحصول على الترجمات
@app.route('/get-i18n')
def get_i18n():
    """
    واجهة برمجة التطبيقات للحصول على جميع الترجمات للعميل
    """
    try:
        translations = i18n_manager.load_translations()
        return jsonify({
            'success': True,
            'translations': translations
        })
    except Exception as e:
        app.logger.error(f"Error al obtener traducciones: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Error al cargar las traducciones'
        })

@app.route('/docs/<path:filename>')
def serve_docs(filename):
    """
    تقديم ملفات الدليل.
    """
    return send_file(os.path.join('docs', filename))

@app.route('/static/css/dynamic.css')
def dynamic_css():
    """
    توليد CSS ديناميكي بناءً على إعدادات التطبيق.
    """
    settings = load_settings()

    # الألوان الافتراضية في حالة عدم وجود إعدادات
    primary_color = settings.get('primary_color', '#0d6efd')
    secondary_color = settings.get('secondary_color', '#6c757d')

    css = f"""
    :root {{
        --primary-color: {primary_color};
        --secondary-color: {secondary_color};
    }}

    .btn-primary {{
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }}

    .btn-primary:hover {{
        background-color: color-mix(in srgb, var(--primary-color) 80%, black);
        border-color: color-mix(in srgb, var(--primary-color) 80%, black);
    }}

    .btn-outline-primary {{
        color: var(--primary-color);
        border-color: var(--primary-color);
    }}

    .btn-outline-primary:hover {{
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }}

    .text-primary {{
        color: var(--primary-color) !important;
    }}

    .bg-primary {{
        background-color: var(--primary-color) !important;
    }}

    .border-primary {{
        border-color: var(--primary-color) !important;
    }}

    a {{
        color: var(--primary-color);
    }}

    a:hover {{
        color: color-mix(in srgb, var(--primary-color) 80%, black);
    }}

    .form-check-input:checked {{
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }}

    .dropdown-item.active, .dropdown-item:active {{
        background-color: var(--primary-color);
    }}

    .page-link {{
        color: var(--primary-color);
    }}

    .page-item.active .page-link {{
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }}

    .progress-bar {{
        background-color: var(--primary-color);
    }}

    /* إعدادات الشعار */
    .navbar-brand img {{
        display: {settings.get('show_logo', 'true') == True and 'block' or 'none'};
    }}

    /* شريط موافقة ملفات تعريف الارتباط */
    .cookie-banner {{
        position: fixed;
        padding: 1rem;
        background-color: #fff;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        width: 100%;
        max-width: 100%;
    }}

    .cookie-banner.bottom {{
        bottom: 0;
        left: 0;
        right: 0;
    }}

    .cookie-banner.top {{
        top: 0;
        left: 0;
        right: 0;
    }}

    .cookie-banner.bottom-left {{
        bottom: 20px;
        left: 20px;
        max-width: 400px;
        border-radius: 8px;
    }}

    .cookie-banner.bottom-right {{
        bottom: 20px;
        right: 20px;
        max-width: 400px;
        border-radius: 8px;
    }}

    .cookie-banner.dark {{
        background-color: #343a40;
        color: #fff;
    }}

    .cookie-banner.light {{
        background-color: #fff;
        color: #212529;
    }}

    .cookie-buttons {{
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }}
    """

    response = Response(css, mimetype='text/css')
    # تعيين رأس التخزين المؤقت لمدة قصيرة (5 دقائق) للسماح بتحديث التغييرات
    response.headers['Cache-Control'] = 'public, max-age=300'
    return response

@app.route('/api/convert', methods=['POST'])
def convert_text():
    """
    تحويل النص من اللاتينية إلى تيفيناغ.

    يتوقع طلب POST مع بيانات JSON تحتوي على حقل 'text'.
    يمكن أن يحتوي على حقل 'hasTable' للإشارة إلى أن النص يحتوي على جداول.

    Returns:
        JSON: يحتوي على النص المحول في حقل 'result'
    """
    try:
        data = request.get_json()
        if not data:
            logger.warning("تم استلام طلب بدون بيانات JSON")
            return jsonify({'error': 'No JSON data provided'}), 400

        if 'text' not in data:
            logger.warning("تم استلام طلب بدون حقل 'text'")
            return jsonify({'error': 'No text provided'}), 400

        latin_text = data['text']
        if not latin_text:
            logger.info("تم استلام نص فارغ للتحويل")
            return jsonify({'result': '', 'hasTable': False})

        has_table = data.get('hasTable', False)

        # تحسين اكتشاف الجداول
        if not has_table:
            has_table = (
                '<table' in latin_text or
                '<tr' in latin_text or
                '<td' in latin_text or
                '<th' in latin_text or
                ('|' in latin_text and (
                    '--' in latin_text or
                    '-:' in latin_text or
                    ':-' in latin_text or
                    latin_text.count('|') >= 4  # على الأقل صفين مع حرفي | في كل منهما
                ))
            )

        # Convert the text
        tifinagh_text = latin_to_tifinagh(latin_text)

        # Log the conversion
        logger.info(f"تم تحويل نص بطول {len(latin_text)} حرف" +
                   (", يحتوي على جداول" if has_table else ""))

        # تحديث الإحصائيات باستخدام الوظيفة المحسنة
        update_stats('text')

        # إضافة النشاط إلى قائمة الأنشطة الأخيرة
        app_stats['recent_activities'].insert(0, {
            'time': time.strftime('%H:%M'),
            'description': f'Text conversion ({len(latin_text)} chars)',
            'type': 'Text',
            'status': 'Success',
            'status_color': 'success'
        })

        # الاحتفاظ فقط بآخر 20 نشاط
        if len(app_stats['recent_activities']) > 20:
            app_stats['recent_activities'] = app_stats['recent_activities'][:20]

        return jsonify({
            'result': tifinagh_text,
            'hasTable': has_table
        })
    except ValueError as e:
        logger.error(f"خطأ في قيمة المدخلات: {str(e)}")
        return jsonify({'error': f'Input error: {str(e)}'}), 400
    except UnicodeError as e:
        logger.error(f"خطأ في ترميز النص: {str(e)}")
        return jsonify({'error': f'Text encoding error: {str(e)}'}), 400
    except Exception as e:
        logger.error(f"خطأ غير متوقع أثناء تحويل النص: {str(e)}")
        return jsonify({'error': f'Conversion error: {str(e)}'}), 500

@app.route('/api/convert-file', methods=['POST'])
def convert_file():
    """
    تحويل ملف نصي من اللاتينية إلى تيفيناغ.

    Returns:
        Flask Response: الملف المحول للتنزيل أو رسالة خطأ
    """
    try:
        # التحقق مما إذا كان طلب POST يحتوي على جزء الملف
        if 'file' not in request.files:
            logger.warning("تم استلام طلب بدون جزء الملف")
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # إذا لم يختر المستخدم ملفًا، فإن المتصفح يرسل
        # جزءًا فارغًا بدون اسم ملف
        if file.filename == '':
            logger.warning("تم استلام طلب بدون اسم ملف")
            return jsonify({'error': 'No selected file'}), 400

        # التحقق من امتداد الملف
        if not allowed_file(file.filename):
            logger.warning(f"نوع الملف غير مسموح به: {file.filename}")
            return jsonify({
                'error': 'File format not supported',
                'details': f'The file format "{os.path.splitext(file.filename)[1]}" is not supported. Supported types: {", ".join(app.config["ALLOWED_EXTENSIONS"])}'
            }), 400

        # التحقق من حجم الملف (10MB كحد أقصى) بطريقة أكثر كفاءة
        # استخدام خاصية content_length من الطلب إذا كانت متوفرة
        content_length = request.content_length
        if content_length and content_length > 10 * 1024 * 1024:  # 10MB
            logger.warning(f"حجم الملف كبير جدًا: {file.filename}, {content_length} bytes")
            return jsonify({
                'error': 'File size exceeds limit',
                'details': 'The maximum file size allowed is 10MB. Please upload a smaller file.'
            }), 413

        # قراءة جزء صغير من الملف للتحقق من وجوده
        chunk = file.read(8192)  # قراءة 8KB فقط
        if not chunk:  # الملف فارغ
            logger.warning(f"الملف فارغ: {file.filename}")
            return jsonify({
                'error': 'Empty file',
                'details': 'The uploaded file is empty.'
            }), 400

        # إعادة مؤشر القراءة إلى بداية الملف
        file.seek(0)

        # تأمين اسم الملف وحفظه
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        logger.info(f"تم تحميل الملف: {filename}")

        # الحصول على تنسيق الإخراج المطلوب
        output_format = request.form.get('output_format', 'html')
        logger.info(f"تنسيق الإخراج المطلوب: {output_format}")

        # معالجة الملف
        result_filepath = process_file(filepath, output_format)

        # إضافة مسار الملف المؤقت إلى قائمة التنظيف
        global temp_files
        temp_files.append(result_filepath)

        # تحديث الإحصائيات باستخدام الوظيفة المحسنة
        update_stats('file')

        # إضافة النشاط إلى قائمة الأنشطة الأخيرة
        app_stats['recent_activities'].insert(0, {
            'time': time.strftime('%H:%M'),
            'description': f'File conversion ({os.path.basename(filepath)})',
            'type': 'File',
            'status': 'Success',
            'status_color': 'success'
        })

        # الاحتفاظ فقط بآخر 20 نشاط
        if len(app_stats['recent_activities']) > 20:
            app_stats['recent_activities'] = app_stats['recent_activities'][:20]

        # حذف الملف الأصلي بعد المعالجة
        try:
            os.remove(filepath)
            logger.info(f"تم حذف الملف الأصلي بعد المعالجة: {filepath}")
        except Exception as e:
            logger.warning(f"لم يتم حذف الملف الأصلي: {filepath}, السبب: {str(e)}")

        # إرجاع الملف المعالج
        return send_file(result_filepath,
                        as_attachment=True,
                        download_name=f"tifinagh_{filename}")

    except FileNotFoundError as e:
        logger.error(f"خطأ: الملف غير موجود: {str(e)}")
        return jsonify({
            'error': 'File not found',
            'details': 'The file could not be processed. It may have been deleted or moved.'
        }), 404
    except UnicodeDecodeError as e:
        logger.error(f"خطأ في ترميز الملف: {str(e)}")
        return jsonify({
            'error': 'Encoding error',
            'details': 'The file contains characters that cannot be processed. Please ensure your file uses UTF-8 or Latin-1 encoding.'
        }), 400
    except IOError as e:
        logger.error(f"خطأ في قراءة/كتابة الملف: {str(e)}")
        return jsonify({
            'error': 'File read/write error',
            'details': 'There was a problem reading or writing the file. The file may be corrupted or inaccessible.'
        }), 500
    except PermissionError as e:
        logger.error(f"خطأ في الصلاحيات: {str(e)}")
        return jsonify({
            'error': 'Permission error',
            'details': 'Unable to access or modify the file. Please check file permissions and try again.'
        }), 403
    except ValueError as e:
        logger.error(f"خطأ في قيمة: {str(e)}")
        return jsonify({
            'error': 'Invalid file content',
            'details': str(e)
        }), 422
    except Exception as e:
        logger.error(f"خطأ غير متوقع أثناء تحويل الملف: {str(e)}")
        return jsonify({
            'error': 'Conversion error',
            'details': 'An unexpected error occurred during file conversion. Please try again or try with a different file.'
        }), 500

@app.route('/api/detect-language', methods=['POST'])
def detect_language():
    """
    اكتشاف لغة النص المدخل.
    يستخدم خوارزمية بسيطة للتعرف على ما إذا كان النص يحتوي على أحرف تيفيناغ أو لاتينية.
    """
    data = request.get_json()
    if not data or 'text' not in data:
        return jsonify({'error': 'No text provided'}), 400

    text = data['text']

    # مجموعة من أحرف تيفيناغ للتعرف عليها
    tifinagh_chars = {'ⴰ', 'ⴱ', 'ⵛ', 'ⴷ', 'ⴻ', 'ⴼ', 'ⴳ', 'ⵀ', 'ⵉ', 'ⵊ', 'ⴽ', 'ⵍ',
                      'ⵎ', 'ⵏ', 'ⵓ', 'ⵒ', 'ⵇ', 'ⵔ', 'ⵙ', 'ⵜ', 'ⵖ', 'ⵡ', 'ⵅ', 'ⵢ', 'ⵣ'}

    # عد الأحرف من كل لغة
    tifinagh_count = sum(1 for char in text if char in tifinagh_chars)

    # حساب النسبة المئوية لأحرف تيفيناغ في النص
    if len(text.strip()) > 0:
        tifinagh_percentage = (tifinagh_count / len(text.strip())) * 100
    else:
        tifinagh_percentage = 0

    # تحديد اللغة بناءً على النسبة المئوية
    if tifinagh_percentage > 30:
        detected_language = 'Tifinagh'
    else:
        detected_language = 'Latin'

    return jsonify({
        'detected_language': detected_language,
        'confidence': tifinagh_percentage if detected_language == 'Tifinagh' else 100 - tifinagh_percentage
    })

@app.route('/simple-converter')
def simple_converter():
    """
    صفحة محول ملفات Word البسيط.
    """
    return render_template('simple_converter.html')

@app.route('/complete-converter')
def complete_converter():
    """
    صفحة محول ملفات Word المتكامل.
    """
    return render_template('complete_converter.html')

@app.route('/api/convert-file-complete', methods=['POST'])
def convert_file_complete():
    """
    تحويل ملف Word إلى تيفيناغ باستخدام الطريقة المتكاملة.

    Returns:
        Flask Response: الملف المحول للتنزيل أو رسالة خطأ
    """
    try:
        # التحقق مما إذا كان طلب POST يحتوي على جزء الملف
        if 'file' not in request.files:
            logger.warning("تم استلام طلب بدون جزء الملف")
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # إذا لم يختر المستخدم ملفًا، فإن المتصفح يرسل
        # جزءًا فارغًا بدون اسم ملف
        if file.filename == '':
            logger.warning("تم استلام طلب بدون اسم ملف")
            return jsonify({'error': 'No selected file'}), 400

        # التحقق من امتداد الملف
        if not file.filename.lower().endswith(('.docx', '.doc')):
            logger.warning(f"نوع الملف غير مسموح به: {file.filename}")
            return jsonify({
                'error': 'File format not supported',
                'details': 'Only Word documents (.docx, .doc) are supported.'
            }), 400

        # التحقق من حجم الملف (10MB كحد أقصى) بطريقة أكثر كفاءة
        # استخدام خاصية content_length من الطلب إذا كانت متوفرة
        content_length = request.content_length
        if content_length and content_length > 10 * 1024 * 1024:  # 10MB
            logger.warning(f"حجم الملف كبير جدًا: {file.filename}, {content_length} bytes")
            return jsonify({
                'error': 'File size exceeds limit',
                'details': 'The maximum file size allowed is 10MB. Please upload a smaller file.'
            }), 413

        # قراءة جزء صغير من الملف للتحقق من وجوده
        chunk = file.read(8192)  # قراءة 8KB فقط
        if not chunk:  # الملف فارغ
            logger.warning(f"الملف فارغ: {file.filename}")
            return jsonify({
                'error': 'Empty file',
                'details': 'The uploaded file is empty.'
            }), 400

        # إعادة مؤشر القراءة إلى بداية الملف
        file.seek(0)

        # تأمين اسم الملف وحفظه
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        logger.info(f"تم تحميل الملف: {filename}")

        # استيراد الدالة المتكاملة لتحويل ملفات Word
        from utils.docx_converter_complete import convert_docx_complete

        # تحديد مسار الملف المخرج
        output_filename = f"tifinagh_{filename}"
        output_filepath = os.path.join(app.config['UPLOAD_FOLDER'], output_filename)

        # تحويل الملف
        result_filepath = convert_docx_complete(filepath, output_filepath)
        logger.info(f"تم تحويل الملف بنجاح: {result_filepath}")

        # إضافة مسار الملف المؤقت إلى قائمة التنظيف
        global temp_files
        temp_files.append(result_filepath)

        # تحديث الإحصائيات باستخدام الوظيفة المحسنة
        update_stats('file')

        # حذف الملف الأصلي بعد المعالجة
        try:
            os.remove(filepath)
            logger.info(f"تم حذف الملف الأصلي بعد المعالجة: {filepath}")
        except Exception as e:
            logger.warning(f"لم يتم حذف الملف الأصلي: {filepath}, السبب: {str(e)}")

        # إرجاع الملف المعالج
        return send_file(result_filepath,
                        as_attachment=True,
                        download_name=output_filename)

    except Exception as e:
        logger.error(f"خطأ غير متوقع أثناء تحويل الملف: {str(e)}")
        return jsonify({
            'error': 'Conversion error',
            'details': str(e)
        }), 500

@app.route('/api/convert-file-simple', methods=['POST'])
def convert_file_simple():
    """
    تحويل ملف Word إلى تيفيناغ باستخدام الطريقة البسيطة.

    Returns:
        Flask Response: الملف المحول للتنزيل أو رسالة خطأ
    """
    try:
        # التحقق مما إذا كان طلب POST يحتوي على جزء الملف
        if 'file' not in request.files:
            logger.warning("تم استلام طلب بدون جزء الملف")
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # إذا لم يختر المستخدم ملفًا، فإن المتصفح يرسل
        # جزءًا فارغًا بدون اسم ملف
        if file.filename == '':
            logger.warning("تم استلام طلب بدون اسم ملف")
            return jsonify({'error': 'No selected file'}), 400

        # التحقق من امتداد الملف
        if not file.filename.lower().endswith(('.docx', '.doc')):
            logger.warning(f"نوع الملف غير مسموح به: {file.filename}")
            return jsonify({
                'error': 'File format not supported',
                'details': 'Only Word documents (.docx, .doc) are supported.'
            }), 400

        # التحقق من حجم الملف (10MB كحد أقصى) بطريقة أكثر كفاءة
        # استخدام خاصية content_length من الطلب إذا كانت متوفرة
        content_length = request.content_length
        if content_length and content_length > 10 * 1024 * 1024:  # 10MB
            logger.warning(f"حجم الملف كبير جدًا: {file.filename}, {content_length} bytes")
            return jsonify({
                'error': 'File size exceeds limit',
                'details': 'The maximum file size allowed is 10MB. Please upload a smaller file.'
            }), 413

        # قراءة جزء صغير من الملف للتحقق من وجوده
        chunk = file.read(8192)  # قراءة 8KB فقط
        if not chunk:  # الملف فارغ
            logger.warning(f"الملف فارغ: {file.filename}")
            return jsonify({
                'error': 'Empty file',
                'details': 'The uploaded file is empty.'
            }), 400

        # إعادة مؤشر القراءة إلى بداية الملف
        file.seek(0)

        # تأمين اسم الملف وحفظه
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        logger.info(f"تم تحميل الملف: {filename}")

        # استيراد الدالة البسيطة لتحويل ملفات Word
        from simple_docx_solution import convert_docx_simple

        # تحديد مسار الملف المخرج
        output_filename = f"tifinagh_{filename}"
        output_filepath = os.path.join(app.config['UPLOAD_FOLDER'], output_filename)

        # تحويل الملف
        result_filepath = convert_docx_simple(filepath, output_filepath)
        logger.info(f"تم تحويل الملف بنجاح: {result_filepath}")

        # إضافة مسار الملف المؤقت إلى قائمة التنظيف
        global temp_files
        temp_files.append(result_filepath)

        # تحديث الإحصائيات باستخدام الوظيفة المحسنة
        update_stats('file')

        # حذف الملف الأصلي بعد المعالجة
        try:
            os.remove(filepath)
            logger.info(f"تم حذف الملف الأصلي بعد المعالجة: {filepath}")
        except Exception as e:
            logger.warning(f"لم يتم حذف الملف الأصلي: {filepath}, السبب: {str(e)}")

        # إرجاع الملف المعالج
        return send_file(result_filepath,
                        as_attachment=True,
                        download_name=output_filename)

    except Exception as e:
        logger.error(f"خطأ غير متوقع أثناء تحويل الملف: {str(e)}")
        return jsonify({
            'error': 'Conversion error',
            'details': str(e)
        }), 500

@app.route('/api/convert-docx', methods=['POST'])
def convert_docx_file():
    """
    تحويل ملف Word (docx) من اللاتينية إلى تيفيناغ.

    يمكن استخدام هذا المسار بطريقتين:
    1. تحميل ملف Word جديد للتحويل
    2. تحويل ملف Word موجود بالفعل في المجلد

    Returns:
        Flask Response: الملف المحول للتنزيل أو رسالة خطأ
    """
    try:
        # التحقق من وجود المكتبات المطلوبة
        required_libraries = check_required_libraries()
        if not required_libraries['all_available']:
            missing_libs = [lib for lib, available in required_libraries.items() if lib != 'all_available' and not available]
            logger.warning(f"المكتبات المفقودة: {', '.join(missing_libs)}")
            return jsonify({
                'error': 'Missing libraries',
                'details': f"The following libraries are required: {', '.join(missing_libs)}"
            }), 500

        # التحقق من وجود ملف مرفق
        if 'file' in request.files:
            file = request.files['file']

            # التحقق من وجود اسم ملف
            if file.filename == '':
                logger.warning("تم استلام طلب بدون اسم ملف")
                return jsonify({'error': 'No selected file'}), 400

            # التحقق من امتداد الملف
            if not file.filename.lower().endswith('.docx'):
                logger.warning(f"نوع الملف غير مسموح به: {file.filename}")
                return jsonify({
                    'error': 'File format not supported',
                    'details': 'Only .docx files are supported for this API endpoint.'
                }), 400

            # التحقق من حجم الملف (10MB كحد أقصى) بطريقة أكثر كفاءة
            # استخدام خاصية content_length من الطلب إذا كانت متوفرة
            content_length = request.content_length
            if content_length and content_length > 10 * 1024 * 1024:  # 10MB
                logger.warning(f"حجم الملف كبير جدًا: {file.filename}, {content_length} bytes")
                return jsonify({
                    'error': 'File size exceeds limit',
                    'details': 'The maximum file size allowed is 10MB. Please upload a smaller file.'
                }), 413

            # قراءة جزء صغير من الملف للتحقق من وجوده
            chunk = file.read(8192)  # قراءة 8KB فقط
            if not chunk:  # الملف فارغ
                logger.warning(f"الملف فارغ: {file.filename}")
                return jsonify({
                    'error': 'Empty file',
                    'details': 'The uploaded file is empty.'
                }), 400

            # إعادة مؤشر القراءة إلى بداية الملف
            file.seek(0)

            # تأمين اسم الملف وحفظه
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            logger.info(f"تم تحميل ملف Word: {filename}")

            # تحويل الملف
            output_path = os.path.join(app.config['UPLOAD_FOLDER'], f"tifinagh_{filename}")
            result_filepath = convert_existing_docx(filepath, output_path)

            # إضافة مسار الملف المؤقت إلى قائمة التنظيف
            temp_files.append(result_filepath)

            # تحديث الإحصائيات باستخدام الوظيفة المحسنة
            update_stats('file')

            # حذف الملف الأصلي بعد المعالجة
            try:
                os.remove(filepath)
                logger.info(f"تم حذف الملف الأصلي بعد المعالجة: {filepath}")
            except Exception as e:
                logger.warning(f"لم يتم حذف الملف الأصلي: {filepath}, السبب: {str(e)}")

            # إرجاع الملف المعالج
            return send_file(result_filepath,
                            as_attachment=True,
                            download_name=f"tifinagh_{filename}")

        # إذا لم يكن هناك ملف مرفق، تحقق من وجود مسار ملف في البيانات
        elif request.is_json:
            data = request.get_json()

            if not data or 'file_path' not in data:
                logger.warning("تم استلام طلب بدون مسار ملف")
                return jsonify({'error': 'No file path provided'}), 400

            file_path = data['file_path']

            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                # محاولة البحث عن الملف في مجلد التحميل
                upload_path = os.path.join(app.config['UPLOAD_FOLDER'], os.path.basename(file_path))
                if os.path.exists(upload_path):
                    file_path = upload_path
                else:
                    logger.error(f"الملف غير موجود: {file_path}")
                    return jsonify({
                        'error': 'File not found',
                        'details': f'The file {file_path} does not exist.'
                    }), 404

            # التحقق من امتداد الملف
            if not file_path.lower().endswith('.docx'):
                logger.warning(f"نوع الملف غير مسموح به: {file_path}")
                return jsonify({
                    'error': 'File format not supported',
                    'details': 'Only .docx files are supported for this API endpoint.'
                }), 400

            # تحويل الملف
            output_path = os.path.join(app.config['UPLOAD_FOLDER'], f"tifinagh_{os.path.basename(file_path)}")
            result_filepath = convert_existing_docx(file_path, output_path)

            # إضافة مسار الملف المؤقت إلى قائمة التنظيف
            temp_files.append(result_filepath)

            # تحديث الإحصائيات باستخدام الوظيفة المحسنة
            update_stats('file')

            # إرجاع مسار الملف المحول
            return jsonify({
                'success': True,
                'message': 'File converted successfully',
                'file_path': result_filepath,
                'download_url': url_for('serve_file', filename=os.path.basename(result_filepath))
            })

        else:
            logger.warning("تم استلام طلب بدون ملف أو مسار ملف")
            return jsonify({
                'error': 'No file provided',
                'details': 'Please provide either a file upload or a file path in JSON format.'
            }), 400

    except ImportError as e:
        logger.error(f"خطأ في استيراد المكتبات: {str(e)}")
        return jsonify({
            'error': 'Library error',
            'details': f'Error importing required libraries: {str(e)}'
        }), 500
    except FileNotFoundError as e:
        logger.error(f"خطأ: الملف غير موجود: {str(e)}")
        return jsonify({
            'error': 'File not found',
            'details': str(e)
        }), 404
    except PermissionError as e:
        logger.error(f"خطأ في الصلاحيات: {str(e)}")
        return jsonify({
            'error': 'Permission error',
            'details': f'Unable to access or modify the file: {str(e)}'
        }), 403
    except Exception as e:
        logger.error(f"خطأ غير متوقع أثناء تحويل ملف Word: {str(e)}")
        return jsonify({
            'error': 'Conversion error',
            'details': f'An unexpected error occurred: {str(e)}'
        }), 500

@app.route('/api/create-docx', methods=['POST'])
def create_docx_file():
    """
    إنشاء ملف Word (docx) جديد مع نص أمازيغي.

    يمكن تحديد ما إذا كان يجب تحويل النص إلى تيفيناغ.

    Returns:
        JSON: معلومات عن الملف المنشأ
    """
    try:
        # التحقق من وجود المكتبات المطلوبة
        libraries_status = check_required_libraries()

        if not libraries_status['python-docx']:
            logger.error("مكتبة python-docx غير متوفرة")
            return jsonify({
                'error': 'Missing library',
                'details': 'The python-docx library is required for this operation. Please install it using: pip install python-docx'
            }), 500

        # الحصول على البيانات
        data = request.get_json() if request.is_json else {}

        # تحديد ما إذا كان يجب تحويل النص إلى تيفيناغ
        convert_to_tifinagh = data.get('convert_to_tifinagh', False)

        # تحديد اسم الملف المخرج
        output_filename = data.get('filename', 'generated_document.docx')
        if not output_filename.lower().endswith('.docx'):
            output_filename += '.docx'

        # تأمين اسم الملف
        output_filename = secure_filename(output_filename)

        # تحديد مسار الملف المخرج
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], output_filename)

        # إنشاء ملف Word جديد
        result_filepath = create_docx(output_path, convert_to_tifinagh)

        # إضافة مسار الملف المؤقت إلى قائمة التنظيف
        temp_files.append(result_filepath)

        # إرجاع معلومات عن الملف المنشأ
        return jsonify({
            'success': True,
            'message': 'Word document created successfully',
            'file_path': result_filepath,
            'download_url': url_for('serve_file', filename=os.path.basename(result_filepath))
        })

    except ImportError as e:
        logger.error(f"خطأ في استيراد المكتبات: {str(e)}")
        return jsonify({
            'error': 'Library error',
            'details': f'Error importing required libraries: {str(e)}'
        }), 500
    except Exception as e:
        logger.error(f"خطأ غير متوقع أثناء إنشاء ملف Word: {str(e)}")
        return jsonify({
            'error': 'Creation error',
            'details': f'An unexpected error occurred: {str(e)}'
        }), 500

@app.route('/uploads/<path:filename>')
def serve_file(filename):
    """
    تقديم ملف من مجلد التحميل.
    """
    return send_file(os.path.join(app.config['UPLOAD_FOLDER'], filename),
                    as_attachment=True,
                    download_name=filename)

@app.route('/api/convert-table-cells', methods=['POST'])
def convert_table_cells():
    """
    تحويل نصوص خلايا الجداول من اللاتينية إلى تيفيناغ.

    يتوقع طلب POST مع بيانات JSON تحتوي على مصفوفة من الخلايا.
    كل خلية تحتوي على معرف ونص.

    Returns:
        JSON: يحتوي على مصفوفة الخلايا مع النصوص المحولة
    """
    data = request.get_json()
    if not data or 'cells' not in data:
        return jsonify({'error': 'No cells provided'}), 400

    cells = data['cells']
    converted_cells = []

    for cell in cells:
        cell_id = cell.get('id')
        text = cell.get('text', '')

        # تحويل النص إلى تيفيناغ
        converted_text = latin_to_tifinagh(text)

        converted_cells.append({
            'id': cell_id,
            'convertedText': converted_text
        })

    logger.info(f"تم تحويل {len(cells)} خلية من خلايا الجداول")

    # تحديث الإحصائيات باستخدام الوظيفة المحسنة
    update_stats('text')

    return jsonify({
        'cells': converted_cells
    })

@app.route('/api/convert-document', methods=['POST'])
def convert_document():
    """
    تحويل محتوى مستند كامل من اللاتينية إلى تيفيناغ.

    يتوقع طلب POST مع بيانات JSON تحتوي على مصفوفة من العقد النصية.
    كل عقدة تحتوي على معرف ونص ونوع (خلية أو فقرة).

    Returns:
        JSON: يحتوي على مصفوفة العقد مع النصوص المحولة
    """
    data = request.get_json()
    if not data or 'nodes' not in data:
        return jsonify({'error': 'No nodes provided'}), 400

    nodes = data['nodes']
    converted_nodes = []

    for node in nodes:
        node_id = node.get('id')
        text = node.get('text', '')
        node_type = node.get('type', 'paragraph')

        # تحويل النص إلى تيفيناغ
        converted_text = latin_to_tifinagh(text)

        converted_nodes.append({
            'id': node_id,
            'convertedText': converted_text,
            'type': node_type
        })

    logger.info(f"تم تحويل {len(nodes)} عقدة من المستند")

    # تحديث الإحصائيات باستخدام الوظيفة المحسنة
    update_stats('text')

    return jsonify({
        'nodes': converted_nodes
    })



@app.route('/api/proxy-website', methods=['GET'])
def proxy_website():
    """
    وكيل لتحميل موقع ويب وتحويل النص فيه إلى تيفيناغ.

    يتوقع معلمة URL في سلسلة الاستعلام.

    Returns:
        HTML: محتوى الموقع المحول
    """
    url = request.args.get('url')
    convert = request.args.get('convert', 'false').lower() == 'true'

    if not url:
        return jsonify({'error': 'No URL provided'}), 400

    # Try to decode URL if it's encoded
    try:
        import urllib.parse
        url = urllib.parse.unquote(url)
        logger.info(f"URL decoded: {url}")
    except Exception as e:
        logger.error(f"Error decoding URL: {str(e)}")

    # التحقق من صحة URL
    if not url.startswith('http://') and not url.startswith('https://'):
        url = 'https://' + url

    try:
        # إضافة User-Agent للتعامل مع المواقع التي تمنع الروبوتات
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # تحميل الموقع
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()  # رفع استثناء إذا كان هناك خطأ في الاستجابة

        # التحقق من نوع المحتوى
        content_type = response.headers.get('Content-Type', '')
        if 'text/html' not in content_type:
            return jsonify({'error': 'URL does not point to an HTML page'}), 400

        # تحليل HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # إضافة قاعدة أساسية لضمان تحميل الموارد النسبية بشكل صحيح
        base_tag = soup.new_tag('base')
        base_tag['href'] = url
        if soup.head:
            soup.head.insert(0, base_tag)
        else:
            # إنشاء عنصر head إذا لم يكن موجودًا
            head = soup.new_tag('head')
            head.append(base_tag)
            if soup.html:
                soup.html.insert(0, head)
            else:
                # إنشاء عنصر html إذا لم يكن موجودًا
                html = soup.new_tag('html')
                html.append(head)
                soup.append(html)

        # تعديل الروابط لتعمل من خلال البروكسي
        for link in soup.find_all('a', href=True):
            href = link['href']

            # تجاهل الروابط الخاصة
            if href.startswith('#') or href.startswith('javascript:') or href.startswith('mailto:'):
                continue

            # تحويل الروابط النسبية إلى روابط مطلقة
            if not href.startswith('http'):
                if href.startswith('/'):
                    base_url = '/'.join(url.split('/')[:3])  # http(s)://domain.com
                    href = base_url + href
                else:
                    href = url.rsplit('/', 1)[0] + '/' + href

            # تحديد ما إذا كان يجب تحويل الرابط من خلال البروكسي
            # تجاهل الروابط الخارجية (التي لا تنتمي إلى نفس النطاق)
            is_same_domain = False
            try:
                from urllib.parse import urlparse
                link_domain = urlparse(href).netloc
                original_domain = urlparse(url).netloc
                is_same_domain = link_domain == original_domain or not link_domain
            except Exception as e:
                logger.warning(f"خطأ في تحليل النطاق: {str(e)}")
                # في حالة الخطأ، نفترض أنه نفس النطاق للأمان
                is_same_domain = True

            # تحويل الروابط الداخلية فقط من خلال البروكسي
            if is_same_domain:
                try:
                    encoded_url = requests.utils.quote(href, safe='')
                    link['href'] = f"/api/proxy-website?url={encoded_url}&convert={str(convert).lower()}"
                except Exception as e:
                    logger.error(f"Error encoding link URL: {str(e)}")
                    link['href'] = f"/api/proxy-website?url={href}&convert={str(convert).lower()}"
            # الاحتفاظ بالروابط الخارجية كما هي
            else:
                # إضافة سمة target="_blank" للروابط الخارجية لفتحها في نافذة جديدة
                link['target'] = '_blank'
                # إضافة سمة rel="noopener noreferrer" للأمان
                link['rel'] = 'noopener noreferrer'

        # تعديل نماذج البحث لتعمل من خلال البروكسي
        for form in soup.find_all('form', action=True):
            action = form['action']

            # تجاهل النماذج بدون action
            if not action or action == '#':
                continue

            # تحويل الروابط النسبية إلى روابط مطلقة
            if not action.startswith('http'):
                if action.startswith('/'):
                    base_url = '/'.join(url.split('/')[:3])  # http(s)://domain.com
                    action = base_url + action
                else:
                    action = url.rsplit('/', 1)[0] + '/' + action

            # Update form action to work through proxy
            try:
                encoded_action = requests.utils.quote(action, safe='')
                form['action'] = f"/api/proxy-website?url={encoded_action}&convert={str(convert).lower()}"
            except Exception as e:
                logger.error(f"Error encoding form action URL: {str(e)}")
                form['action'] = f"/api/proxy-website?url={action}&convert={str(convert).lower()}"

            # إضافة حقل مخفي للحفاظ على حالة التحويل
            hidden_input = soup.new_tag('input')
            hidden_input['type'] = 'hidden'
            hidden_input['name'] = 'convert'
            hidden_input['value'] = str(convert).lower()
            form.append(hidden_input)

        # إضافة نص JavaScript لتحويل النص إلى تيفيناغ
        script_tag = soup.new_tag('script')
        script_tag['type'] = 'text/javascript'

        # كود JavaScript للتحويل
        script_content = """
        (function() {
            // Latin to Tifinagh mapping
            const latinToTifinagh = {
                'a': 'ⴰ', 'b': 'ⴱ', 'c': 'ⵛ', 'd': 'ⴷ', 'e': 'ⴻ',
                'f': 'ⴼ', 'g': 'ⴳ', 'h': 'ⵀ', 'i': 'ⵉ', 'j': 'ⵊ',
                'k': 'ⴽ', 'l': 'ⵍ', 'm': 'ⵎ', 'n': 'ⵏ', 'o': 'ⵓ',
                'p': 'ⵃ', 'q': 'ⵇ', 'r': 'ⵔ', 's': 'ⵙ', 't': 'ⵜ',
                'u': 'ⵓ', 'v': 'ⵖ', 'w': 'ⵡ', 'x': 'ⵅ', 'y': 'ⵢ',
                'z': 'ⵣ',
                'A': 'ⴰ', 'B': 'ⴱ', 'C': 'ⵛ', 'D': 'ⴷ', 'E': 'ⴻ',
                'F': 'ⴼ', 'G': 'ⴳ', 'H': 'ⵀ', 'I': 'ⵉ', 'J': 'ⵊ',
                'K': 'ⴽ', 'L': 'ⵍ', 'M': 'ⵎ', 'N': 'ⵏ', 'O': 'ⵓ',
                'P': 'ⵃ', 'Q': 'ⵇ', 'R': 'ⵔ', 'S': 'ⵙ', 'T': 'ⵜ',
                'U': 'ⵓ', 'V': 'ⵖ', 'W': 'ⵡ', 'X': 'ⵅ', 'Y': 'ⵢ',
                'Z': 'ⵣ',
                // Special characters
                'ɣ': 'ⵖ', 'ḥ': 'ⵃ', 'ɛ': 'ⵄ', 'ε': 'ⵄ', 'ḍ': 'ⴹ',
                'ṣ': 'ⵚ', 'ṭ': 'ⵟ', 'ẓ': 'ⵥ',
                // Additional special characters
                'kˇ': 'ⴽⵯ', 'gˇ': 'ⴳⵯ', 'dj': 'ⴷⵊ', 'tc': 'ⵜⵛ',
                'ḇ': 'ⴲ', '†': 'ⴿ', 'ṯ': 'ⵝ', 'ḏ': 'ⴸ', 'ṛ': 'ⵕ',
                'š': 'ⴴ', 'ã': 'ⵚ', 'ï': 'ⵟ', 'ä': 'ⴹ', 'ç': 'ⵥ',
                'å': 'ⵯ', 'œ': 'ⵕ', 'ø': 'ⵖ', 'ë': 'ⵕ',
                // Additional uppercase variants
                'Ṛ': 'ⵕ', 'Ɣ': 'ⵖ', 'Ḥ': 'ⵃ'
            };

            // Store original text content
            const originalTextContent = new WeakMap();

            // Convert function
            function convertText(text) {
                if (!text) return '';

                // تقسيم النص إلى كلمات وفواصل للتعامل مع حالات خاصة
                const tokens = text.match(/\\S+|\\s+/g) || [];
                const processedTokens = [];

                // دالة لمعالجة كلمة واحدة
                function processWord(word) {
                    if (!word) return "";

                    // التحويل الأساسي باستخدام قاموس التعيين
                    const regex = new RegExp(Object.keys(latinToTifinagh).join('|'), 'gi');
                    let result = word.replace(regex, match => latinToTifinagh[match.toLowerCase()] || match);

                    // معالجة حرف "e" بشكل خاص
                    result = result.replace(/e/gi, (match, offset, string) => {
                        const i = offset;

                        // الحصول على الأحرف المحيطة
                        const prevChar = string[i-1] || '';
                        const nextChar = string[i+1] || '';
                        const prevPrevChar = string[i-2] || '';
                        const nextNextChar = string[i+2] || '';

                        // في البداية، نفترض أننا سنحذف الحرف "e"
                        let eResult = '';

                        // إذا كان الحرف "e" بين حرفين صامتين متطابقين
                        if (prevChar === nextChar && prevChar !== 'e') {
                            eResult = "ⴻ";
                        }
                        // إذا كان الحرف "e" بين ثلاثة صوامت متطابقة
                        else if ((prevPrevChar === prevChar && prevChar === nextChar && prevChar !== 'e') ||
                            (prevChar === nextChar && nextChar === nextNextChar && prevChar !== 'e')) {
                            eResult = "ⴻ";
                        }
                        // إذا كان الحرف "e" في وسط أربعة حروف، اثنين منهم متماثلين، والآخرين مختلفين ولكنهم متماثلين
                        // مثل: "ttedd"، "qqenn"، "tteqq"، يتم حذف "e"
                        else if ((prevPrevChar === prevChar && nextChar === nextNextChar && prevChar !== nextChar) ||
                            (prevPrevChar === nextChar && prevChar === nextNextChar && prevPrevChar !== prevChar)) {
                            eResult = ''; // يتم حذف الحرف "e"
                        }
                        // إذا جاء في وسط أربعة حروف من حرف "t"، لا يتم حذفه
                        else if (prevPrevChar === 't' && prevChar === 't' && nextChar === 't' && nextNextChar === 't') {
                            eResult = "ⴻ";
                        }

                        return eResult;
                    });

                    // معالجة الشرطة "-" وسط الكلمة
                    result = result.replace(/-/g, (match, offset) => {
                        const i = offset;

                        // التحقق مما إذا كانت الشرطة وسط كلمة (بين حرفين أبجديين)
                        if (i > 0 && i < word.length - 1) {
                            const prevChar = word[i-1];
                            const nextChar = word[i+1];

                            // التحقق مما إذا كان الحرف السابق والتالي أحرفًا أبجدية
                            if (/[a-zA-Z\\u0600-\\u06FF\\u0750-\\u077F\\u2D30-\\u2D7F]/.test(prevChar) &&
                                /[a-zA-Z\\u0600-\\u06FF\\u0750-\\u077F\\u2D30-\\u2D7F]/.test(nextChar)) {
                                // الشرطة بين حرفين أبجديين، نحذفها
                                return '';
                            }
                        }

                        // الاحتفاظ بالشرطة في الحالات الأخرى
                        return '-';
                    });

                    return result;
                }

                // معالجة كل رمز (كلمة أو مسافة)
                for (const token of tokens) {
                    if (/\\s+/.test(token)) {
                        // إذا كان الرمز مجرد مسافات، نحتفظ به كما هو
                        processedTokens.push(token);
                    } else {
                        // معالجة الكلمة
                        const processedWord = processWord(token);
                        processedTokens.push(processedWord);
                    }
                }

                return processedTokens.join('');
            }

            // Process text nodes
            function processNode(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    // Text node
                    if (!originalTextContent.has(node)) {
                        originalTextContent.set(node, node.nodeValue);
                    }

                    const text = originalTextContent.get(node);
                    node.nodeValue = convertText(text);
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    // Element node
                    // Skip script and style elements
                    if (node.tagName === 'SCRIPT' || node.tagName === 'STYLE' || node.tagName === 'NOSCRIPT') {
                        return;
                    }

                    // Process child nodes
                    for (let i = 0; i < node.childNodes.length; i++) {
                        processNode(node.childNodes[i]);
                    }
                }
            }

            // No need for indicator as we have the toolbar

            // Initialize
            function init() {
                processNode(document.body);
                console.log('Latin to Tifinagh converter initialized');
            }

            // Run conversion when DOM is fully loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
            } else {
                init();
            }

            // Handle dynamic content loading
            // This ensures conversion works when content changes without page reload

            // Create a MutationObserver to watch for DOM changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    // Process only added nodes
                    if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                        for (let i = 0; i < mutation.addedNodes.length; i++) {
                            const node = mutation.addedNodes[i];
                            if (node.nodeType === 1) { // Element node
                                processNode(node);
                            }
                        }
                    }
                });
            });

            // Start observing the document with the configured parameters
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Handle navigation within the page
            document.addEventListener('click', function(e) {
                // Check if the click is on a link
                let target = e.target;
                while (target && target.tagName !== 'A') {
                    target = target.parentElement;
                }

                // If it's a link and it's an internal link
                if (target && target.tagName === 'A' &&
                    target.href &&
                    target.href.startsWith(window.location.origin) &&
                    !target.getAttribute('download') &&
                    target.target !== '_blank') {

                    // Add a small delay to allow the new page to load
                    setTimeout(function() {
                        // Re-run the conversion on the new page content
                        processNode(document.body);
                    }, 500);
                }
            });

            // Also handle history changes (back/forward navigation)
            window.addEventListener('popstate', function() {
                // Add a small delay to allow the new page to load
                setTimeout(function() {
                    // Re-run the conversion on the new page content
                    processNode(document.body);
                }, 500);
            });

            // Handle AJAX content loading
            // Intercept XMLHttpRequest to detect AJAX content changes
            const originalXHROpen = XMLHttpRequest.prototype.open;
            const originalXHRSend = XMLHttpRequest.prototype.send;

            XMLHttpRequest.prototype.open = function() {
                this._tifinagh_method = arguments[0];
                this._tifinagh_url = arguments[1];
                return originalXHROpen.apply(this, arguments);
            };

            XMLHttpRequest.prototype.send = function() {
                const xhr = this;
                const originalOnReadyStateChange = xhr.onreadystatechange;

                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        // Wait a bit for the content to be inserted into the DOM
                        setTimeout(function() {
                            processNode(document.body);
                        }, 500);
                    }

                    if (originalOnReadyStateChange) {
                        originalOnReadyStateChange.apply(xhr, arguments);
                    }
                };

                return originalXHRSend.apply(this, arguments);
            };
        })();
        """

        # إضافة النص البرمجي إلى الصفحة إذا كان التحويل مطلوبًا
        if convert:
            if soup.body:
                script_tag.string = script_content
                soup.body.append(script_tag)

        # Add CSS to improve Tifinagh text display
        style_tag = soup.new_tag('style')
        style_tag.string = """
        :root {
            --primary-color: #4f46e5;
            --primary-rgb: 79, 70, 229;
            --primary-hover: #4338ca;
            --text-color: #111827;
            --text-light: #6b7280;
            --bg-color: #ffffff;
            --bg-light: #f9fafb;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --transition-speed: 0.3s;
        }

        @font-face {
            font-family: 'Noto Sans Tifinagh';
            src: url('/static/fonts/NotoSansTifinagh-Regular.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Inter';
            src: url('/static/fonts/Inter-Regular.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        body, p, h1, h2, h3, h4, h5, h6, span, div {
            font-family: 'Inter', 'Noto Sans Tifinagh', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important;
            line-height: 1.5;
        }

        a {
            transition: all var(--transition-speed) ease;
        }

        button, input, select, textarea {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        """

        if soup.head:
            soup.head.append(style_tag)

        # Add toolbar for conversion control
        toolbar_div = soup.new_tag('div')
        toolbar_div['id'] = 'tifinagh-toolbar'
        toolbar_div['style'] = """
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: var(--primary-color, #4f46e5);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 99999;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            direction: ltr;
        """

        # Left section of the toolbar
        left_div = soup.new_tag('div')
        left_div['style'] = "display: flex; align-items: center;"

        logo_span = soup.new_tag('span')
        logo_span['style'] = "font-weight: bold; margin-right: 15px; font-size: 18px;"
        logo_span.string = "ⵜⵉⴼⵉⵏⴰⵖ"

        left_div.append(logo_span)

        status_span = soup.new_tag('span')
        status_span['style'] = "font-size: 14px;"
        if convert:
            status_span.string = "Tifinagh Conversion: Enabled"
        else:
            status_span.string = "Tifinagh Conversion: Disabled"
        left_div.append(status_span)

        # Right section of the toolbar
        right_div = soup.new_tag('div')
        right_div['style'] = "display: flex; align-items: center; gap: 15px;"

        # Toggle conversion button
        toggle_link = soup.new_tag('a')
        try:
            encoded_url = requests.utils.quote(url, safe='')
            toggle_link['href'] = f"/api/proxy-website?url={encoded_url}&convert={'false' if convert else 'true'}"
        except Exception as e:
            logger.error(f"Error encoding toggle URL: {str(e)}")
            toggle_link['href'] = f"/api/proxy-website?url={url}&convert={'false' if convert else 'true'}"
        toggle_link['style'] = """
            color: white;
            text-decoration: none;
            background-color: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 14px;
            transition: all var(--transition-speed, 0.3s) ease;
        """
        toggle_link['onmouseover'] = "this.style.backgroundColor='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'"
        toggle_link['onmouseout'] = "this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'"
        toggle_link.string = "Disable Conversion" if convert else "Enable Conversion"
        right_div.append(toggle_link)

        # Back to converter button
        back_link = soup.new_tag('a')
        back_link['href'] = '/'
        back_link['style'] = """
            color: white;
            text-decoration: none;
            background-color: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 14px;
            transition: all var(--transition-speed, 0.3s) ease;
        """
        back_link['onmouseover'] = "this.style.backgroundColor='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'"
        back_link['onmouseout'] = "this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'"
        back_link.string = "Back to Converter"
        right_div.append(back_link)

        # Add sections to the toolbar
        toolbar_div.append(left_div)
        toolbar_div.append(right_div)

        # Add JavaScript to ensure toolbar visibility and enhance interaction
        ensure_toolbar_script = soup.new_tag('script')
        ensure_toolbar_script.string = """
            // Ensure toolbar is always visible and enhance interaction
            (function() {
                // Keep toolbar visible
                setInterval(function() {
                    var toolbar = document.getElementById('tifinagh-toolbar');
                    if (!toolbar || toolbar.style.display === 'none') {
                        location.reload();
                    }
                }, 2000);

                // Add hover effect to toolbar
                var toolbar = document.getElementById('tifinagh-toolbar');
                if (toolbar) {
                    // Add minimize/maximize functionality
                    var isMinimized = false;
                    var minimizeBtn = document.createElement('div');
                    minimizeBtn.style.position = 'absolute';
                    minimizeBtn.style.right = '10px';
                    minimizeBtn.style.top = '50%';
                    minimizeBtn.style.transform = 'translateY(-50%)';
                    minimizeBtn.style.cursor = 'pointer';
                    minimizeBtn.style.fontSize = '18px';
                    minimizeBtn.style.color = 'white';
                    minimizeBtn.style.width = '24px';
                    minimizeBtn.style.height = '24px';
                    minimizeBtn.style.display = 'flex';
                    minimizeBtn.style.alignItems = 'center';
                    minimizeBtn.style.justifyContent = 'center';
                    minimizeBtn.style.borderRadius = '50%';
                    minimizeBtn.style.transition = 'all 0.3s ease';
                    minimizeBtn.style.backgroundColor = 'rgba(255,255,255,0.1)';
                    minimizeBtn.innerHTML = '−';
                    minimizeBtn.title = 'Minimize/Maximize Toolbar';

                    minimizeBtn.onmouseover = function() {
                        this.style.backgroundColor = 'rgba(255,255,255,0.2)';
                    };

                    minimizeBtn.onmouseout = function() {
                        this.style.backgroundColor = 'rgba(255,255,255,0.1)';
                    };

                    minimizeBtn.onclick = function() {
                        var leftDiv = toolbar.querySelector('div:first-child');
                        var rightDiv = toolbar.querySelector('div:last-child');

                        if (isMinimized) {
                            // Maximize
                            if (leftDiv) leftDiv.style.display = 'flex';
                            if (rightDiv) rightDiv.style.display = 'flex';
                            minimizeBtn.innerHTML = '−';
                            isMinimized = false;
                        } else {
                            // Minimize
                            if (leftDiv) leftDiv.style.display = 'none';
                            if (rightDiv) rightDiv.style.display = 'none';
                            minimizeBtn.innerHTML = '+';
                            isMinimized = true;
                        }
                    };

                    toolbar.style.position = 'relative';
                    toolbar.appendChild(minimizeBtn);
                }
            })();
        """

        if soup.body:
            soup.body.insert(0, toolbar_div)
            soup.body.append(ensure_toolbar_script)

            # إضافة هامش علوي للجسم لتجنب تداخل شريط الأدوات
            if not soup.body.get('style'):
                soup.body['style'] = ""
            soup.body['style'] += "margin-top: 55px;"

            # تحديث الإحصائيات باستخدام الوظيفة المحسنة
            update_stats('web')

        # إرجاع الصفحة المعدلة
        return Response(str(soup), content_type='text/html; charset=utf-8')

    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching website: {str(e)}")
        return jsonify({'error': f'Error fetching website: {str(e)}'}), 500
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return jsonify({'error': f'Unexpected error: {str(e)}'}), 500

# Translation Management API Endpoints
@app.route('/get-translations', methods=['GET'])
def get_translations():
    """
    API endpoint to get all translations.

    Returns:
        JSON response with all translations
    """
    try:
        from utils.translation_utils import load_translations, get_translation_statistics
        translations = load_translations()
        statistics = get_translation_statistics()

        return jsonify({
            'success': True,
            'translations': translations,
            'statistics': statistics
        })
    except Exception as e:
        logger.error(f"Error getting translations: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error getting translations: {str(e)}'
        }), 500

@app.route('/save-translation', methods=['POST'])
def save_translation():
    """
    API endpoint to save translations.

    Expects JSON data in the format:
    {
        "translations": {
            "key1": {"en": "English text", "am": "Amazigh text"},
            "key2": {"en": "English text", "am": "Amazigh text"},
            ...
        }
    }

    Returns:
        JSON response with success status and message
    """
    try:
        data = request.get_json()

        if not data or 'translations' not in data:
            return jsonify({
                'success': False,
                'message': 'Invalid data format. Expected "translations" object.'
            }), 400

        translations_data = data['translations']

        # Save translations
        from utils.translation_utils import save_translation_batch, get_translation_statistics
        success, message = save_translation_batch(translations_data)

        if success:
            # Get updated statistics
            statistics = get_translation_statistics()

            return jsonify({
                'success': True,
                'message': message,
                'statistics': statistics
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 500
    except Exception as e:
        logger.error(f"Error saving translations: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error saving translations: {str(e)}'
        }), 500

@app.route('/update-translations', methods=['POST'])
def update_translations():
    """
    API endpoint to update translations by extracting all texts from the application.

    Returns:
        JSON response with success status, message, and statistics
    """
    try:
        from utils.translation_utils import update_translations_from_extracted_texts, get_translation_statistics

        # Update translations
        success, translations, message = update_translations_from_extracted_texts()

        if success:
            # Get updated statistics
            statistics = get_translation_statistics()

            return jsonify({
                'success': True,
                'message': message,
                'statistics': statistics
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 500
    except Exception as e:
        logger.error(f"Error updating translations: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error updating translations: {str(e)}'
        }), 500

@app.route('/get-translation-statistics', methods=['GET'])
def get_translation_stats():
    """
    API endpoint to get translation statistics.

    Returns:
        JSON response with translation statistics
    """
    try:
        from utils.translation_utils import get_translation_statistics

        statistics = get_translation_statistics()

        return jsonify({
            'success': True,
            'statistics': statistics
        })
    except Exception as e:
        logger.error(f"Error getting translation statistics: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error getting translation statistics: {str(e)}'
        }), 500


@app.before_request
def before_request():
    """
    تنفيذ إجراءات قبل كل طلب
    """
    try:
        # تحميل إعدادات مواقع التواصل الاجتماعي النشطة
        from social_media import get_active_social_media, SUPPORTED_SOCIAL_MEDIA
        import flask

        # إضافة إعدادات مواقع التواصل الاجتماعي النشطة إلى متغيرات القالب
        g = flask.g
        g.active_social_media = get_active_social_media()
        g.social_media_info = SUPPORTED_SOCIAL_MEDIA
    except Exception as e:
        logger.error(f"Error loading social media settings: {str(e)}")


if __name__ == '__main__':
    app.run(debug=True, port=5001)




