/**
 * ملف اختبار لفحص أشرطة التمرير
 * يمكن استخدامه في وضع التطوير للتأكد من عمل الحل بشكل صحيح
 */

// دالة لفحص أشرطة التمرير
function testScrollbars() {
    console.log('🔍 بدء فحص أشرطة التمرير...');
    
    const results = {
        mainScrollbar: false,
        editorScrollbar: false,
        noExtraScrollbars: true,
        responsiveWorking: true
    };
    
    // فحص شريط التمرير الرئيسي
    const htmlOverflow = window.getComputedStyle(document.documentElement).overflowY;
    results.mainScrollbar = htmlOverflow === 'scroll';
    console.log(`📊 شريط التمرير الرئيسي: ${results.mainScrollbar ? '✅' : '❌'} (${htmlOverflow})`);
    
    // فحص شريط تمرير المحرر
    const editorContent = document.querySelector('.document-editor-content');
    if (editorContent) {
        const editorOverflow = window.getComputedStyle(editorContent).overflowY;
        results.editorScrollbar = editorOverflow === 'auto';
        console.log(`📝 شريط تمرير المحرر: ${results.editorScrollbar ? '✅' : '❌'} (${editorOverflow})`);
    } else {
        console.log('📝 المحرر غير موجود في الصفحة الحالية');
    }
    
    // فحص عدم وجود أشرطة تمرير إضافية
    const containers = document.querySelectorAll('.advanced-editor-wrapper, #document-editor, #advanced-converter');
    containers.forEach((container, index) => {
        const overflow = window.getComputedStyle(container).overflow;
        if (overflow !== 'visible') {
            results.noExtraScrollbars = false;
            console.log(`⚠️ حاوية ${index + 1} لديها overflow: ${overflow}`);
        }
    });
    
    if (results.noExtraScrollbars) {
        console.log('🚫 لا توجد أشرطة تمرير إضافية: ✅');
    }
    
    // فحص الاستجابة للشاشات المختلفة
    const ckContent = document.querySelector('.ck-content');
    if (ckContent) {
        const maxHeight = window.getComputedStyle(ckContent).maxHeight;
        const expectedHeight = window.innerWidth <= 480 ? '350px' : 
                              window.innerWidth <= 768 ? '400px' : '500px';
        results.responsiveWorking = maxHeight === expectedHeight;
        console.log(`📱 الاستجابة للشاشات: ${results.responsiveWorking ? '✅' : '❌'} (${maxHeight} vs ${expectedHeight})`);
    }
    
    // النتيجة النهائية
    const allPassed = Object.values(results).every(result => result === true);
    console.log(`\n🎯 النتيجة النهائية: ${allPassed ? '✅ جميع الاختبارات نجحت' : '❌ بعض الاختبارات فشلت'}`);
    
    return results;
}

// دالة لفحص التداخل في أشرطة التمرير
function detectScrollbarOverlap() {
    console.log('🔍 فحص تداخل أشرطة التمرير...');
    
    const scrollableElements = document.querySelectorAll('*');
    const scrollbars = [];
    
    scrollableElements.forEach(element => {
        const style = window.getComputedStyle(element);
        if (style.overflowY === 'scroll' || style.overflowY === 'auto') {
            const rect = element.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
                scrollbars.push({
                    element: element,
                    selector: element.tagName + (element.className ? '.' + element.className.split(' ').join('.') : ''),
                    rect: rect
                });
            }
        }
    });
    
    console.log(`📊 عدد العناصر القابلة للتمرير: ${scrollbars.length}`);
    scrollbars.forEach((item, index) => {
        console.log(`${index + 1}. ${item.selector}`);
    });
    
    return scrollbars;
}

// تشغيل الاختبارات عند تحميل الصفحة
if (window.location.search.includes('debug=scrollbar')) {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            console.log('🚀 بدء اختبارات أشرطة التمرير...');
            testScrollbars();
            detectScrollbarOverlap();
        }, 2000);
    });
}

// تصدير الدوال للاستخدام في وحدة التحكم
window.testScrollbars = testScrollbars;
window.detectScrollbarOverlap = detectScrollbarOverlap;
