/**
 * إصلاح شامل لنظام تبديل اللغة
 * يضمن عمل تبديل اللغة بشكل صحيح في جميع الحالات
 */

(function() {
    'use strict';
    
    let isInitialized = false;
    let retryCount = 0;
    const maxRetries = 50; // 5 ثوان بحد أقصى
    
    console.log('🔧 بدء تحميل إصلاح تبديل اللغة...');
    
    function debugLog(message, data = null) {
        console.log(`[Language Fix] ${message}`, data || '');
    }
    
    function initLanguageSwitching() {
        if (isInitialized) {
            debugLog('تم التهيئة مسبقاً - تجاهل');
            return;
        }
        
        debugLog('بدء تهيئة نظام تبديل اللغة...');
        
        // التحقق من وجود نظام الترجمة
        if (!window.i18n) {
            debugLog('نظام الترجمة غير موجود');
            return false;
        }
        
        if (!window.i18n.isLoaded) {
            debugLog('نظام الترجمة لم يتم تحميله بعد');
            return false;
        }
        
        // البحث عن أزرار اللغة
        const languageButtons = document.querySelectorAll('.lang-btn');
        debugLog('تم العثور على أزرار اللغة:', languageButtons.length);
        
        if (languageButtons.length === 0) {
            debugLog('لم يتم العثور على أزرار اللغة');
            return false;
        }
        
        // إزالة مستمعي الأحداث السابقين
        languageButtons.forEach((button, index) => {
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            debugLog(`تم استنساخ الزر ${index + 1}`);
        });
        
        // الحصول على الأزرار الجديدة
        const newLanguageButtons = document.querySelectorAll('.lang-btn');
        
        // إضافة مستمعي الأحداث الجدد
        newLanguageButtons.forEach((button, index) => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const lang = this.getAttribute('data-lang');
                debugLog(`تم النقر على زر اللغة: ${lang}`);
                
                if (!lang) {
                    debugLog('لا يوجد data-lang في الزر');
                    return;
                }
                
                // تغيير اللغة
                try {
                    window.i18n.setLanguage(lang);
                    debugLog(`تم تغيير اللغة إلى: ${lang}`);
                    
                    // تحديث حالة الأزرار
                    updateButtonStates(lang);
                    
                    // تحديث نص القائمة المنسدلة
                    updateDropdownText(this);
                    
                    // حفظ في localStorage
                    localStorage.setItem('selectedLanguage', lang);
                    localStorage.setItem('preferred_language', lang);
                    
                    debugLog('تم حفظ اللغة في localStorage');
                    
                } catch (error) {
                    debugLog('خطأ في تغيير اللغة:', error);
                }
            });
            
            debugLog(`تم إعداد مستمع الأحداث للزر ${index + 1}`);
        });
        
        // تحديث حالة الأزرار الحالية
        const currentLang = window.i18n.currentLang || 'en';
        updateButtonStates(currentLang);
        
        isInitialized = true;
        debugLog('✅ تم إعداد نظام تبديل اللغة بنجاح');
        return true;
    }
    
    function updateButtonStates(selectedLang) {
        debugLog(`تحديث حالة الأزرار للغة: ${selectedLang}`);
        
        const languageButtons = document.querySelectorAll('.lang-btn');
        
        languageButtons.forEach(button => {
            const buttonLang = button.getAttribute('data-lang');
            const checkIcon = button.querySelector('.bi-check2');
            
            // إزالة الحالة النشطة من جميع الأزرار
            button.classList.remove('active');
            button.removeAttribute('aria-current');
            
            if (checkIcon) {
                if (buttonLang === selectedLang) {
                    // إظهار أيقونة التحقق للغة المختارة
                    checkIcon.classList.remove('invisible');
                    button.classList.add('active');
                    button.setAttribute('aria-current', 'true');
                } else {
                    // إخفاء أيقونة التحقق للغات الأخرى
                    checkIcon.classList.add('invisible');
                }
            }
        });
        
        debugLog('تم تحديث حالة الأزرار');
    }
    
    function updateDropdownText(selectedButton) {
        const currentLanguageDisplay = document.querySelector('.current-language');
        if (currentLanguageDisplay && selectedButton) {
            const textSpan = selectedButton.querySelector('span:last-child');
            if (textSpan) {
                const clonedSpan = textSpan.cloneNode(true);
                currentLanguageDisplay.innerHTML = '';
                currentLanguageDisplay.appendChild(clonedSpan);
                debugLog('تم تحديث نص القائمة المنسدلة');
            }
        }
    }
    
    function attemptInitialization() {
        retryCount++;
        debugLog(`محاولة التهيئة رقم: ${retryCount}`);
        
        if (retryCount > maxRetries) {
            debugLog('❌ تم الوصول للحد الأقصى من المحاولات');
            return;
        }
        
        if (initLanguageSwitching()) {
            debugLog('✅ تم إعداد نظام تبديل اللغة بنجاح');
            
            // تحميل اللغة المحفوظة
            const savedLang = localStorage.getItem('selectedLanguage') || 
                             localStorage.getItem('preferred_language') || 'en';
            
            if (savedLang && savedLang !== window.i18n.currentLang) {
                debugLog(`تحميل اللغة المحفوظة: ${savedLang}`);
                window.i18n.setLanguage(savedLang);
                updateButtonStates(savedLang);
            }
        } else {
            // إعادة المحاولة بعد 100ms
            setTimeout(attemptInitialization, 100);
        }
    }
    
    // بدء عملية التهيئة عند تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', attemptInitialization);
    } else {
        // DOM جاهز بالفعل
        setTimeout(attemptInitialization, 100);
    }
    
    // مستمع إضافي لحدث تحميل نظام الترجمة
    document.addEventListener('languageSystemLoaded', function() {
        debugLog('تم استلام حدث languageSystemLoaded');
        setTimeout(attemptInitialization, 100);
    });
    
    // مستمع لحدث تغيير اللغة
    document.addEventListener('languageChanged', function(e) {
        debugLog('تم استلام حدث languageChanged:', e.detail.language);
        updateButtonStates(e.detail.language);
    });
    
    // إضافة دالة عامة للاختبار
    window.testLanguageSwitch = function(lang) {
        debugLog(`اختبار تبديل اللغة إلى: ${lang}`);
        if (window.i18n && window.i18n.setLanguage) {
            window.i18n.setLanguage(lang);
            updateButtonStates(lang);
            return true;
        }
        return false;
    };
    
    debugLog('تم تحميل إصلاح تبديل اللغة');
    
})();
