/**
 * نظام الترجمة (i18n) للتطبيق
 */

class I18nSystem {
    constructor() {
        this.translations = {};
        this.currentLang = 'en'; // اللغة الافتراضية
        this.isLoaded = false;
        this.onLoadCallbacks = [];
    }

    /**
     * تحميل ملف الترجمة
     * @returns {Promise} وعد يتم حله عند اكتمال تحميل الترجمات
     */
    loadTranslations() {
        return fetch('/get-i18n')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`فشل تحميل الترجمات: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    this.translations = data.translations;
                    this.isLoaded = true;

                    // استدعاء جميع الدوال المسجلة للتنفيذ بعد تحميل الترجمات
                    this.onLoadCallbacks.forEach(callback => callback());
                    this.onLoadCallbacks = [];

                    // تحميل اللغة المحفوظة من قبل المستخدم
                    const savedLang = localStorage.getItem('preferred_language') || localStorage.getItem('selectedLanguage');
                    if (savedLang) {
                        this.currentLang = savedLang;
                    }

                    // إطلاق حدث اكتمال تحميل نظام الترجمة
                    const event = new CustomEvent('languageSystemLoaded', { detail: { translations: this.translations } });
                    document.dispatchEvent(event);

                    return this.translations;
                } else {
                    throw new Error(data.message || 'Failed to load translations');
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الترجمات:', error);
                return {};
            });
    }

    /**
     * تسجيل دالة ليتم استدعاؤها بعد تحميل الترجمات
     * @param {Function} callback الدالة المراد تنفيذها
     */
    onLoad(callback) {
        if (this.isLoaded) {
            callback();
        } else {
            this.onLoadCallbacks.push(callback);
        }
    }

    /**
     * الحصول على ترجمة نص معين
     * @param {string} key مفتاح النص
     * @param {Object} params معلمات لاستبدالها في النص
     * @returns {string} النص المترجم
     */
    translate(key, params = {}) {
        if (!key) return '';

        // التحقق من وجود المفتاح
        if (this.translations[key]) {
            // إرجاع الترجمة باللغة المحددة إذا كانت متوفرة
            if (this.translations[key][this.currentLang]) {
                let text = this.translations[key][this.currentLang];

                // استبدال المعلمات في النص
                if (params && Object.keys(params).length > 0) {
                    Object.keys(params).forEach(param => {
                        const regex = new RegExp(`{${param}}`, 'g');
                        text = text.replace(regex, params[param]);
                    });
                }

                return text;
            }

            // إرجاع الترجمة باللغة الإنجليزية كاحتياطي
            if (this.translations[key]['en']) {
                let text = this.translations[key]['en'];

                // استبدال المعلمات في النص
                if (params && Object.keys(params).length > 0) {
                    Object.keys(params).forEach(param => {
                        const regex = new RegExp(`{${param}}`, 'g');
                        text = text.replace(regex, params[param]);
                    });
                }

                return text;
            }
        }

        // إرجاع المفتاح نفسه إذا لم يتم العثور على ترجمة
        return key;
    }

    /**
     * تغيير اللغة الحالية وتحديث واجهة المستخدم
     * @param {string} lang رمز اللغة
     */
    setLanguage(lang) {
        if (lang === this.currentLang) return;

        this.currentLang = lang;
        localStorage.setItem('preferred_language', lang);
        this.updateUI();

        // كلتا اللغتين من اليسار إلى اليمين
        document.documentElement.setAttribute('dir', 'ltr');
        document.body.classList.add('ltr');
        document.body.classList.remove('rtl');

        // إطلاق حدث تغيير اللغة
        const event = new CustomEvent('languageChanged', { detail: { language: lang } });
        document.dispatchEvent(event);
    }

    /**
     * تحديث جميع النصوص في واجهة المستخدم
     */
    updateUI() {
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const text = this.translate(key);

            // تطبيق الترجمة على العنصر
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                // إذا كان العنصر حقل إدخال أو منطقة نص
                if (element.getAttribute('placeholder')) {
                    element.setAttribute('placeholder', text);
                } else {
                    element.value = text;
                }
            } else if (element.tagName === 'IMG') {
                // إذا كان العنصر صورة
                element.setAttribute('alt', text);
            } else {
                // العناصر الأخرى
                element.innerHTML = text;
            }
        });
    }

    /**
     * تهيئة نظام الترجمة
     */
    init() {
        // تحميل الترجمات
        this.loadTranslations()
            .then(() => {
                // تحديث واجهة المستخدم بعد تحميل الترجمات
                this.updateUI();

                // إعداد مستمعي الأحداث لعناصر تبديل اللغة - تم نقله إلى main.js لتجنب التضارب
                // سيتم التعامل مع هذا في main.js
                console.log('نظام الترجمة جاهز - سيتم إعداد أزرار اللغة في main.js');

                // تعيين اتجاه الصفحة (كلتا اللغتين من اليسار إلى اليمين)
                document.documentElement.setAttribute('dir', 'ltr');
                document.body.classList.add('ltr');
                document.body.classList.remove('rtl');
            })
            .catch(error => {
                console.error('فشل تهيئة نظام الترجمة:', error);
            });
    }
}

// إنشاء كائن عام لنظام الترجمة
window.i18n = new I18nSystem();

// تهيئة نظام الترجمة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.i18n.init();
});

// دالة مساعدة عامة للترجمة
window.__ = function(key, params) {
    return window.i18n.translate(key, params);
};
