/**
 * Custom style# advanced-editor-container .ck-content {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-color: var(--border-color);
    min-height: 450px;
    max-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    overflow-y: visible !important; /* منع ظهور شريط تمرير مزدوج */
}itor 5
 */

/* Editor container */
#advanced-editor-container .ck-editor {
    border-radius: 4px;
    overflow: hidden;
}

/* Editor toolbar */
#advanced-editor-container .ck-toolbar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #f9f9f9;
    border-color: var(--border-color);
}

/* Editor content area */
#advanced-editor-container .ck-content {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-color: var(--border-color);
    min-height: 450px;
    max-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    overflow-y: visible !important; /* منع ظهور شريط تمرير مزدوج */
}

/* Table styles within the editor */
.ck-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    border: 1px solid var(--border-color);
}

.ck-content td,
.ck-content th {
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    text-align: left;
    vertical-align: top;
    min-width: 2rem;
}

.ck-content th {
    background-color: var(--bg-light);
    font-weight: bold;
}

/* Custom font styles */
.ck-content .text-aref {
    font-family: 'aref', 'Aref Ruqaa', serif;
}

.ck-content .text-mirza {
    font-family: 'mirza', Arial, sans-serif;
}

.ck-content .text-roboto {
    font-family: 'roboto', Arial, sans-serif;
}

/* Table styles in the output area */
.tifinagh-text table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
}

.tifinagh-text table td,
.tifinagh-text table th {
    border: 1px solid #ddd;
    padding: 8px;
}

.tifinagh-text table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* Zebra striping for table rows */
.tifinagh-text table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Hover effect for table rows */
.tifinagh-text table tr:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .ck-toolbar {
        flex-wrap: wrap;
    }

    .ck-content table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}
