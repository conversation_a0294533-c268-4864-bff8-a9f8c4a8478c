/**
 * ملف CSS مخصص لضمان عمل شريط التمرير في المحرر البسيط
 * يطبق أولوية عالية لضمان عدم تداخل ملفات أشرطة التمرير الأخرى
 */

/* ضمان ظهور شريط التمرير في المحرر البسيط - أولوية قصوى */
.text-area textarea,
.text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important; /* Firefox */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ */

    /* ضمان الأبعاد الصحيحة */
    min-height: 500px !important;
    max-height: 500px !important;
    height: auto !important; /* السماح بالتمدد حسب المحتوى */

    /* ض<PERSON>ان عدم إخفاء شريط التمرير */
    box-sizing: border-box !important;

    /* إعادة تعيين أي إعدادات قد تخفي شريط التمرير */
    display: block !important;
    visibility: visible !important;
}

/* تخصيص مظهر شريط التمرير في المحرر البسيط */
.text-area textarea::-webkit-scrollbar,
.text-area .tifinagh-text::-webkit-scrollbar {
    display: block !important;
    width: 8px !important;
    height: 8px !important;
    background: transparent !important;
}

.text-area textarea::-webkit-scrollbar-track,
.text-area .tifinagh-text::-webkit-scrollbar-track {
    background: #f8f9fa !important;
    border-radius: 4px !important;
    margin: 2px !important;
}

.text-area textarea::-webkit-scrollbar-thumb,
.text-area .tifinagh-text::-webkit-scrollbar-thumb {
    background: #dee2e6 !important;
    border-radius: 4px !important;
    border: 1px solid #f8f9fa !important;
    min-height: 20px !important;
}

.text-area textarea::-webkit-scrollbar-thumb:hover,
.text-area .tifinagh-text::-webkit-scrollbar-thumb:hover {
    background: #adb5bd !important;
}

.text-area textarea::-webkit-scrollbar-thumb:active,
.text-area .tifinagh-text::-webkit-scrollbar-thumb:active {
    background: #6c757d !important;
}

.text-area textarea::-webkit-scrollbar-corner,
.text-area .tifinagh-text::-webkit-scrollbar-corner {
    background: #f8f9fa !important;
}

/* إعدادات للشاشات المختلفة */
@media (max-width: 768px) {
    .text-area textarea,
    .text-area .tifinagh-text {
        min-height: 400px !important;
        max-height: 500px !important;
        height: auto !important; /* السماح بالتمدد في الشاشات الصغيرة */
    }
    
    .text-area textarea::-webkit-scrollbar,
    .text-area .tifinagh-text::-webkit-scrollbar {
        width: 6px !important;
    }
}

@media (max-width: 480px) {
    .text-area textarea,
    .text-area .tifinagh-text {
        min-height: 350px !important;
        max-height: 450px !important;
    }
    
    .text-area textarea::-webkit-scrollbar,
    .text-area .tifinagh-text::-webkit-scrollbar {
        width: 5px !important;
    }
}

/* ضمان عدم تأثر المحرر البسيط بإعدادات أشرطة التمرير الأخرى */
#simple-converter .text-area textarea,
#simple-converter .text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;

    /* إعادة تعيين أي إعدادات قد تخفي شريط التمرير */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إعدادات إضافية للمحرر البسيط */
.simple-converter .text-area textarea,
.simple-converter .text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
}

/* ضمان ظهور شريط التمرير في جميع الحالات */
#latin-text,
#tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
    min-height: 500px !important;
    max-height: 500px !important;
}

/* إزالة أي تأثيرات قد تخفي شريط التمرير */
#simple-converter .text-area textarea::-webkit-scrollbar,
#simple-converter .text-area .tifinagh-text::-webkit-scrollbar {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* ضمان عمل التمرير في جميع الحالات */
.simple-converter .text-area,
#simple-converter .text-area {
    overflow: visible !important;
}

.simple-converter .text-area textarea,
.simple-converter .text-area .tifinagh-text,
#simple-converter .text-area textarea,
#simple-converter .text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
}
