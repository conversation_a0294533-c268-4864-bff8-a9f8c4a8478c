/**
 * Main JavaScript functionality for Latin to Tifinagh Converter
 */

document.addEventListener('DOMContentLoaded', function() {
    // Tab switching is now handled by unified-tabs.js

    // انتظار تحميل نظام الترجمة قبل إعداد أزرار اللغة
    function initLanguageButtons() {
        const languageButtons = document.querySelectorAll('.lang-btn');

        // Language button click handler
        languageButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = button.getAttribute('data-lang');

                // استخدام نظام الترجمة الجديد إذا كان متاحًا
                if (window.i18n) {
                    window.i18n.setLanguage(lang);

                    // تحديث حالة الأزرار
                    updateLanguageButtonsState(lang);

                    // تحديث النص في زر القائمة المنسدلة
                    updateDropdownButtonText(button);

                    // حفظ اختيار المستخدم
                    localStorage.setItem('selectedLanguage', lang);
                } else {
                    console.error('نظام الترجمة غير متاح');
                }
            });
        });
    }

    // تحديث حالة أزرار اللغة
    function updateLanguageButtonsState(lang) {
        // تحديث أزرار اللغة في القائمة المنسدلة
        languageButtons.forEach(btn => {
            // إزالة الفئة النشطة من جميع الأزرار
            btn.classList.remove('active');

            // إزالة سمة aria-current
            btn.removeAttribute('aria-current');

            // إظهار أو إخفاء أيقونة التحقق
            const checkIcon = btn.querySelector('.bi-check2');
            if (checkIcon) {
                if (btn.getAttribute('data-lang') === lang) {
                    checkIcon.classList.remove('invisible');
                } else {
                    checkIcon.classList.add('invisible');
                }
            }
        });

        // إضافة الفئة النشطة وسمة aria-current إلى اللغة المحددة
        const selectedButton = document.querySelector(`.lang-btn[data-lang="${lang}"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
            selectedButton.setAttribute('aria-current', 'true');
        }
    }

    // تحديث النص في زر القائمة المنسدلة
    function updateDropdownButtonText(button) {
        const currentLanguageText = button.querySelector('span:last-child').textContent;
        const currentLanguageDisplay = document.querySelector('.current-language');
        if (currentLanguageDisplay) {
            // نسخ محتوى العنصر بدلاً من استخدام textContent
            const spanElement = button.querySelector('span:last-child').cloneNode(true);
            currentLanguageDisplay.innerHTML = '';
            currentLanguageDisplay.appendChild(spanElement);
        }
    }

    // تهيئة أزرار اللغة
    if (window.i18n && window.i18n.isLoaded) {
        initLanguageButtons();
    } else {
        // انتظار تحميل نظام الترجمة
        document.addEventListener('languageSystemLoaded', initLanguageButtons);

        // أو انتظار تحميل نظام الترجمة بطريقة أخرى
        setTimeout(() => {
            if (window.i18n) {
                window.i18n.onLoad(initLanguageButtons);
            }
        }, 100);
    }

    // تحميل تفضيل اللغة المحفوظ من localStorage
    const savedLanguage = localStorage.getItem('selectedLanguage') || localStorage.getItem('preferred_language');
    if (savedLanguage && window.i18n) {
        window.i18n.onLoad(function() {
            window.i18n.setLanguage(savedLanguage);
            updateLanguageButtonsState(savedLanguage);

            const langButton = document.querySelector(`.lang-btn[data-lang="${savedLanguage}"]`);
            if (langButton) {
                updateDropdownButtonText(langButton);
            }
        });
    }
});
