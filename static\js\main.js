/**
 * Main JavaScript functionality for Latin to Tifinagh Converter
 */

document.addEventListener('DOMContentLoaded', function() {
    // Tab switching is now handled by unified-tabs.js

    // انتظار تحميل نظام الترجمة قبل إعداد أزرار اللغة
    function initLanguageButtons() {
        console.log('بدء تهيئة أزرار اللغة...');
        const languageButtons = document.querySelectorAll('.lang-btn');
        console.log('تم العثور على', languageButtons.length, 'أزرار لغة');

        // إزالة مستمعي الأحداث السابقين لتجنب التكرار
        languageButtons.forEach(button => {
            // إنشاء نسخة جديدة من العنصر لإزالة جميع مستمعي الأحداث
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
        });

        // الحصول على الأزرار الجديدة بعد الاستنساخ
        const newLanguageButtons = document.querySelectorAll('.lang-btn');

        // Language button click handler
        newLanguageButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const lang = button.getAttribute('data-lang');
                console.log('تم النقر على زر اللغة:', lang);

                // استخدام نظام الترجمة الجديد إذا كان متاحًا
                if (window.i18n && window.i18n.isLoaded) {
                    console.log('تغيير اللغة إلى:', lang);
                    window.i18n.setLanguage(lang);

                    // تحديث حالة الأزرار
                    updateLanguageButtonsState(lang);

                    // تحديث النص في زر القائمة المنسدلة
                    updateDropdownButtonText(button);

                    // حفظ اختيار المستخدم
                    localStorage.setItem('selectedLanguage', lang);
                    localStorage.setItem('preferred_language', lang);

                    console.log('تم تغيير اللغة بنجاح إلى:', lang);
                } else {
                    console.error('نظام الترجمة غير متاح أو لم يتم تحميله بعد');
                }
            });
        });

        console.log('تم إعداد أزرار اللغة بنجاح');
    }

    // تحديث حالة أزرار اللغة
    function updateLanguageButtonsState(lang) {
        // الحصول على جميع أزرار اللغة
        const languageButtons = document.querySelectorAll('.lang-btn');

        // تحديث أزرار اللغة في القائمة المنسدلة
        languageButtons.forEach(btn => {
            // إزالة الفئة النشطة من جميع الأزرار
            btn.classList.remove('active');

            // إزالة سمة aria-current
            btn.removeAttribute('aria-current');

            // إظهار أو إخفاء أيقونة التحقق
            const checkIcon = btn.querySelector('.bi-check2');
            if (checkIcon) {
                if (btn.getAttribute('data-lang') === lang) {
                    checkIcon.classList.remove('invisible');
                } else {
                    checkIcon.classList.add('invisible');
                }
            }
        });

        // إضافة الفئة النشطة وسمة aria-current إلى اللغة المحددة
        const selectedButton = document.querySelector(`.lang-btn[data-lang="${lang}"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
            selectedButton.setAttribute('aria-current', 'true');
        }
    }

    // تحديث النص في زر القائمة المنسدلة
    function updateDropdownButtonText(button) {
        const currentLanguageText = button.querySelector('span:last-child').textContent;
        const currentLanguageDisplay = document.querySelector('.current-language');
        if (currentLanguageDisplay) {
            // نسخ محتوى العنصر بدلاً من استخدام textContent
            const spanElement = button.querySelector('span:last-child').cloneNode(true);
            currentLanguageDisplay.innerHTML = '';
            currentLanguageDisplay.appendChild(spanElement);
        }
    }

    // تهيئة أزرار اللغة مع آلية انتظار محسنة
    function waitForI18nAndInit() {
        if (window.i18n && window.i18n.isLoaded) {
            console.log('نظام الترجمة جاهز - بدء تهيئة أزرار اللغة');
            initLanguageButtons();
        } else if (window.i18n) {
            console.log('نظام الترجمة موجود لكن لم يتم تحميله بعد - انتظار...');
            window.i18n.onLoad(initLanguageButtons);
        } else {
            console.log('نظام الترجمة غير موجود - انتظار...');
            setTimeout(waitForI18nAndInit, 100);
        }
    }

    // بدء عملية الانتظار والتهيئة
    waitForI18nAndInit();

    // إضافة مستمع إضافي للحدث المخصص
    document.addEventListener('languageSystemLoaded', function() {
        console.log('تم استلام حدث languageSystemLoaded');
        initLanguageButtons();
    });

    // تحميل تفضيل اللغة المحفوظ من localStorage
    const savedLanguage = localStorage.getItem('selectedLanguage') || localStorage.getItem('preferred_language');
    if (savedLanguage && window.i18n) {
        window.i18n.onLoad(function() {
            window.i18n.setLanguage(savedLanguage);
            updateLanguageButtonsState(savedLanguage);

            const langButton = document.querySelector(`.lang-btn[data-lang="${savedLanguage}"]`);
            if (langButton) {
                updateDropdownButtonText(langButton);
            }
        });
    }
});
