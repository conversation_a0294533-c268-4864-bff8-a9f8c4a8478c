/**
 * Scrollbar Fix CSS
 * يحل مشكلة انحياز العناصر عند ظهور شريط التمرير
 */

/* تطبيق شريط التمرير دائمًا على عنصر HTML */
html {
    overflow-y: scroll !important; /* إظهار شريط التمرير دائماً لمنع انحياز العناصر */
}

/* إزالة تنسيقات شريط التمرير المخصصة */
* {
    scrollbar-width: auto !important; /* إعادة تعيين عرض شريط التمرير إلى الافتراضي */
}

/* تأكيد أن المحتوى الرئيسي لا يتأثر بشريط التمرير */
main.main-content {
    overflow-x: hidden !important;
    width: 100% !important;
}

/* تأكيد أن الفوتر يظهر بشكل صحيح */
footer {
    width: 100% !important;
}
