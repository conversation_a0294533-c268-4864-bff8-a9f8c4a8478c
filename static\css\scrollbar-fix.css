/**
 * حل موحد لأشرطة التمرير في التطبيق
 * يظهر شريط التمرير الداخلي للمحرر فقط ويخفي شريط التمرير الرئيسي
 */

/* إخفاء شريط التمرير الرئيسي للصفحة */
html {
    overflow: hidden !important; /* إخفاء شريط التمرير الرئيسي */
    height: 100%;
}

body {
    overflow: hidden !important; /* إخفاء شريط التمرير في الجسم */
    height: 100vh;
    margin: 0;
    padding: 0;
}

/* إعدادات المحرر المتقدم - منع أشرطة التمرير الخارجية */
.advanced-editor-wrapper {
    overflow: hidden !important; /* منع أي تمرير خارجي */
    height: auto !important;
}

#document-editor {
    overflow: hidden !important; /* منع أي تمرير خارجي */
    height: auto !important;
}

#advanced-converter {
    overflow: hidden !important; /* منع أي تمرير خارجي */
    height: 100vh !important; /* ارتفاع كامل للشاشة */
    display: flex !important;
    flex-direction: column !important;
}

/* إعدادات الحاوية الرئيسية للمحتوى */
main.main-content {
    overflow: hidden !important;
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
}

/* منطقة المحتوى - شريط التمرير الداخلي الوحيد */
.document-editor-content {
    overflow-y: auto !important; /* شريط التمرير الداخلي الوحيد */
    overflow-x: hidden !important;
    max-height: calc(100vh - 200px) !important; /* ارتفاع ديناميكي */
    min-height: 450px !important;
    flex: 1 !important; /* يأخذ المساحة المتاحة */
}

/* إعدادات CKEditor - شريط التمرير الداخلي الوحيد */
.ck-editor__editable,
.ck-content {
    overflow-y: auto !important; /* شريط التمرير الداخلي الوحيد */
    overflow-x: hidden !important;
    max-height: calc(100vh - 200px) !important; /* ارتفاع ديناميكي */
    min-height: 450px !important;
    flex: 1 !important; /* يأخذ المساحة المتاحة */
}

/* منع أشرطة التمرير في جميع الحاويات الخارجية */
.tab-content,
.tab-pane,
.container,
.container-fluid,
.row,
.col,
[class*="col-"] {
    overflow: hidden !important;
    height: auto !important;
}

/* إعدادات خاصة للفوتر */
footer {
    width: 100% !important;
    overflow: hidden !important;
    flex-shrink: 0 !important; /* منع تقليص الفوتر */
}

/* تخصيص مظهر شريط التمرير الداخلي الوحيد */
.document-editor-content::-webkit-scrollbar,
.ck-content::-webkit-scrollbar {
    width: 12px; /* عرض أكبر لسهولة الاستخدام */
    height: 12px;
}

.document-editor-content::-webkit-scrollbar-track,
.ck-content::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 6px;
    margin: 2px;
}

.document-editor-content::-webkit-scrollbar-thumb,
.ck-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 6px;
    border: 2px solid #f1f3f4;
    min-height: 20px;
}

.document-editor-content::-webkit-scrollbar-thumb:hover,
.ck-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.document-editor-content::-webkit-scrollbar-thumb:active,
.ck-content::-webkit-scrollbar-thumb:active {
    background: #909090;
}

.document-editor-content::-webkit-scrollbar-corner,
.ck-content::-webkit-scrollbar-corner {
    background: #f1f3f4;
}

/* إعدادات للشاشات المختلفة */
@media (max-width: 768px) {
    .document-editor-content,
    .ck-content {
        max-height: calc(100vh - 150px) !important;
        min-height: 350px !important;
    }

    /* شريط تمرير أصغر للشاشات المتوسطة */
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar {
        width: 10px;
    }
}

@media (max-width: 480px) {
    .document-editor-content,
    .ck-content {
        max-height: calc(100vh - 120px) !important;
        min-height: 300px !important;
    }

    /* شريط تمرير أصغر للشاشات الصغيرة */
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar {
        width: 8px;
    }
}

/* إخفاء أي أشرطة تمرير أخرى قد تظهر */
*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable) {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable)::-webkit-scrollbar {
    display: none !important; /* Safari and Chrome */
}
