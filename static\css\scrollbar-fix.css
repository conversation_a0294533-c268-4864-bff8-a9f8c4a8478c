/**
 * حل موحد لأشرطة التمرير في التطبيق
 * يمنع ظهور أشرطة تمرير متعددة ويضمن تجربة مستخدم سلسة
 */

/* ضمان ظهور شريط التمرير الرئيسي للصفحة دائماً */
html {
    overflow-y: scroll !important; /* إظهار شريط التمرير دائماً لمنع انحياز العناصر */
    overflow-x: hidden;
}

body {
    overflow-x: hidden;
    overflow-y: visible;
}

/* إعدادات المحرر المتقدم - منع أشرطة التمرير الخارجية */
.advanced-editor-wrapper {
    overflow: visible !important;
    height: auto !important;
}

#document-editor {
    overflow: visible !important;
    height: auto !important;
}

#advanced-converter {
    overflow: visible !important;
    height: auto !important;
}

/* منطقة المحتوى - شريط تمرير داخلي عند الحاجة فقط */
.document-editor-content {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: 500px !important;
    min-height: 450px !important;
}

/* إعدادات CKEditor */
.ck-editor__editable,
.ck-content {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    max-height: 500px !important;
    min-height: 450px !important;
}

/* منع أشرطة التمرير الإضافية في الحاويات الخارجية */
.tab-content,
.tab-pane {
    overflow: visible !important;
    height: auto !important;
}

/* تأكيد أن المحتوى الرئيسي لا يتأثر بشريط التمرير */
main.main-content {
    overflow-x: hidden !important;
    width: 100% !important;
}

/* تأكيد أن الفوتر يظهر بشكل صحيح */
footer {
    width: 100% !important;
}

/* تخصيص مظهر شريط التمرير للمتصفحات المبنية على Webkit */
.document-editor-content::-webkit-scrollbar,
.ck-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.document-editor-content::-webkit-scrollbar-track,
.ck-content::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.document-editor-content::-webkit-scrollbar-thumb,
.ck-content::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 4px;
    border: 1px solid #f8f9fa;
}

.document-editor-content::-webkit-scrollbar-thumb:hover,
.ck-content::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

.document-editor-content::-webkit-scrollbar-corner,
.ck-content::-webkit-scrollbar-corner {
    background: #f8f9fa;
}

/* إعدادات للشاشات الصغيرة */
@media (max-width: 768px) {
    .document-editor-content,
    .ck-content {
        max-height: 400px !important;
        min-height: 350px !important;
    }
}

@media (max-width: 480px) {
    .document-editor-content,
    .ck-content {
        max-height: 350px !important;
        min-height: 300px !important;
    }
}
