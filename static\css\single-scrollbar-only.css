/**
 * ملف CSS لضمان ظهور شريط تمرير واحد فقط (المحرر الداخلي)
 * يخفي جميع أشرطة التمرير الأخرى في الصفحة
 */

/* إخفاء شريط التمرير الرئيسي للصفحة بالكامل */
html, body {
    overflow: hidden !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* إخفاء شريط التمرير في Webkit browsers */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* إخفاء أشرطة التمرير في جميع العناصر عدا المحرر */
*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable) {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable)::-webkit-scrollbar {
    display: none !important; /* Safari and Chrome */
    width: 0 !important;
    height: 0 !important;
}

/* التأكد من إخفاء أشرطة التمرير في الحاويات الشائعة */
.container,
.container-fluid,
.row,
.col,
[class*="col-"],
.tab-content,
.tab-pane,
.advanced-editor-wrapper,
#document-editor,
#advanced-converter,
main,
section,
div:not(.document-editor-content):not(.ck-content) {
    overflow: hidden !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.container::-webkit-scrollbar,
.container-fluid::-webkit-scrollbar,
.row::-webkit-scrollbar,
.col::-webkit-scrollbar,
[class*="col-"]::-webkit-scrollbar,
.tab-content::-webkit-scrollbar,
.tab-pane::-webkit-scrollbar,
.advanced-editor-wrapper::-webkit-scrollbar,
#document-editor::-webkit-scrollbar,
#advanced-converter::-webkit-scrollbar,
main::-webkit-scrollbar,
section::-webkit-scrollbar,
div:not(.document-editor-content):not(.ck-content)::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* السماح بشريط التمرير في المحرر فقط */
.document-editor-content,
.ck-content,
.ck-editor__editable {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important; /* Firefox */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ */
}

/* تخصيص مظهر شريط التمرير الوحيد المسموح */
.document-editor-content::-webkit-scrollbar,
.ck-content::-webkit-scrollbar,
.ck-editor__editable::-webkit-scrollbar {
    display: block !important;
    width: 12px !important;
    height: 12px !important;
}

.document-editor-content::-webkit-scrollbar-track,
.ck-content::-webkit-scrollbar-track,
.ck-editor__editable::-webkit-scrollbar-track {
    background: #f1f3f4 !important;
    border-radius: 6px !important;
    margin: 2px !important;
}

.document-editor-content::-webkit-scrollbar-thumb,
.ck-content::-webkit-scrollbar-thumb,
.ck-editor__editable::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 6px !important;
    border: 2px solid #f1f3f4 !important;
    min-height: 20px !important;
}

.document-editor-content::-webkit-scrollbar-thumb:hover,
.ck-content::-webkit-scrollbar-thumb:hover,
.ck-editor__editable::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

.document-editor-content::-webkit-scrollbar-thumb:active,
.ck-content::-webkit-scrollbar-thumb:active,
.ck-editor__editable::-webkit-scrollbar-thumb:active {
    background: #909090 !important;
}

/* إعدادات خاصة للشاشات الصغيرة */
@media (max-width: 768px) {
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar,
    .ck-editor__editable::-webkit-scrollbar {
        width: 10px !important;
    }
}

@media (max-width: 480px) {
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar,
    .ck-editor__editable::-webkit-scrollbar {
        width: 8px !important;
    }
}

/* إخفاء أي أشرطة تمرير قد تظهر في Bootstrap أو مكتبات أخرى */
.modal,
.modal-body,
.modal-content,
.dropdown-menu,
.navbar,
.nav,
.card,
.card-body {
    overflow: hidden !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.modal::-webkit-scrollbar,
.modal-body::-webkit-scrollbar,
.modal-content::-webkit-scrollbar,
.dropdown-menu::-webkit-scrollbar,
.navbar::-webkit-scrollbar,
.nav::-webkit-scrollbar,
.card::-webkit-scrollbar,
.card-body::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}
