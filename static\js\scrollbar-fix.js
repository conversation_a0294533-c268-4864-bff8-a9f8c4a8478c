/**
 * Final solution to fix the scrollbar issues
 * This script runs after all other scripts and applies fixes dynamically
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to fix scrollbar issues across the application
    function fixScrollbarIssues() {
        // Fix CKEditor related elements - restore scrollbars
        const editorContent = document.querySelector('.document-editor-content');
        if (editorContent) {
            editorContent.style.overflowY = 'auto';
            editorContent.style.maxHeight = '500px';
        }
        
        // Restore scrollbars on editor elements
        const editorElements = document.querySelectorAll('.ck-editor__editable, .ck-content');
        editorElements.forEach(el => {
            if (el) {
                el.style.overflowY = 'auto';
                el.style.maxHeight = '500px';
            }
        });
        
        // Fix tabs container
        const advancedTab = document.getElementById('advanced-converter');
        if (advancedTab) {
            advancedTab.style.height = 'auto';
            advancedTab.style.overflow = 'visible';
        }
        
        // Fix editor wrapper
        const editorWrapper = document.querySelector('.advanced-editor-wrapper');
        if (editorWrapper) {
            editorWrapper.style.overflow = 'visible';
        }
        
        // Fix document editor
        const documentEditor = document.getElementById('document-editor');
        if (documentEditor) {
            documentEditor.style.overflow = 'visible';
        }
    }
    
    // Run fix immediately
    fixScrollbarIssues();
    
    // Setup observer for content changes
    const observer = new MutationObserver(function(mutations) {
        fixScrollbarIssues();
    });
    
    // Observe the document body for changes
    observer.observe(document.body, { 
        childList: true, 
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });
    
    // Also fix when window is resized
    window.addEventListener('resize', fixScrollbarIssues);
    
    // Apply fix when tab is switched
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            setTimeout(fixScrollbarIssues, 100);
        });
    });
    
    // Export the fix function
    window.fixScrollbars = fixScrollbarIssues;
});
