/**
 * حل موحد لإصلاح مشاكل أشرطة التمرير
 * يظهر شريط التمرير الداخلي للمحرر فقط ويخفي جميع الأشرطة الأخرى
 */
document.addEventListener('DOMContentLoaded', function() {

    // دالة لإصلاح مشاكل أشرطة التمرير
    function fixScrollbarIssues() {
        // إخفاء شريط التمرير الرئيسي للصفحة
        document.documentElement.style.overflow = 'hidden';
        document.documentElement.style.height = '100%';
        document.body.style.overflow = 'hidden';
        document.body.style.height = '100vh';
        document.body.style.margin = '0';
        document.body.style.padding = '0';

        // إعداد الحاوية الرئيسية
        const mainContent = document.querySelector('main.main-content');
        if (mainContent) {
            mainContent.style.overflow = 'hidden';
            mainContent.style.height = '100vh';
            mainContent.style.display = 'flex';
            mainContent.style.flexDirection = 'column';
        }

        // إصلاح منطقة محتوى المحرر - الشريط الوحيد المسموح
        const editorContent = document.querySelector('.document-editor-content');
        if (editorContent) {
            editorContent.style.overflowY = 'auto';
            editorContent.style.overflowX = 'hidden';
            editorContent.style.maxHeight = 'calc(100vh - 200px)';
            editorContent.style.minHeight = '450px';
            editorContent.style.flex = '1';
        }

        // إصلاح عناصر CKEditor - الشريط الوحيد المسموح
        const editorElements = document.querySelectorAll('.ck-editor__editable, .ck-content');
        editorElements.forEach(el => {
            if (el) {
                el.style.overflowY = 'auto';
                el.style.overflowX = 'hidden';
                el.style.maxHeight = 'calc(100vh - 200px)';
                el.style.minHeight = '450px';
                el.style.flex = '1';
            }
        });

        // منع أشرطة التمرير في جميع الحاويات الخارجية
        const outerContainers = document.querySelectorAll(
            '.advanced-editor-wrapper, #document-editor, #advanced-converter, .tab-content, .tab-pane, .container, .container-fluid, .row, .col, [class*="col-"]'
        );
        outerContainers.forEach(container => {
            if (container) {
                container.style.overflow = 'hidden';
                container.style.height = 'auto';
            }
        });

        // إعداد خاص للمحرر المتقدم
        const advancedConverter = document.getElementById('advanced-converter');
        if (advancedConverter) {
            advancedConverter.style.overflow = 'hidden';
            advancedConverter.style.height = '100vh';
            advancedConverter.style.display = 'flex';
            advancedConverter.style.flexDirection = 'column';
        }

        // إعداد الفوتر
        const footer = document.querySelector('footer');
        if (footer) {
            footer.style.overflow = 'hidden';
            footer.style.flexShrink = '0';
        }

        // إصلاح خاص للشاشات المختلفة
        const responsiveElements = document.querySelectorAll('.document-editor-content, .ck-content');
        responsiveElements.forEach(el => {
            if (el) {
                if (window.innerWidth <= 480) {
                    el.style.maxHeight = 'calc(100vh - 120px)';
                    el.style.minHeight = '300px';
                } else if (window.innerWidth <= 768) {
                    el.style.maxHeight = 'calc(100vh - 150px)';
                    el.style.minHeight = '350px';
                } else {
                    el.style.maxHeight = 'calc(100vh - 200px)';
                    el.style.minHeight = '450px';
                }
            }
        });

        // إخفاء أي أشرطة تمرير أخرى قد تظهر
        const allElements = document.querySelectorAll('*');
        allElements.forEach(el => {
            if (!el.matches('.document-editor-content, .ck-content, .ck-editor__editable')) {
                el.style.scrollbarWidth = 'none'; // Firefox
                el.style.msOverflowStyle = 'none'; // IE
            }
        });
    }

    // تشغيل الإصلاح فوراً
    fixScrollbarIssues();

    // إعداد مراقب للتغييرات في DOM
    const observer = new MutationObserver(function(mutations) {
        let shouldFix = false;
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' ||
                (mutation.type === 'attributes' &&
                 ['style', 'class'].includes(mutation.attributeName))) {
                shouldFix = true;
            }
        });

        if (shouldFix) {
            setTimeout(fixScrollbarIssues, 50);
        }
    });

    // مراقبة التغييرات في الجسم
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });

    // إصلاح عند تغيير حجم النافذة
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(fixScrollbarIssues, 100);
    });

    // إصلاح عند تبديل التبويبات
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            setTimeout(fixScrollbarIssues, 150);
        });
    });

    // إصلاح عند تحميل CKEditor
    if (window.DecoupledEditor) {
        const originalCreate = window.DecoupledEditor.create;
        window.DecoupledEditor.create = function(...args) {
            return originalCreate.apply(this, args).then(editor => {
                setTimeout(fixScrollbarIssues, 200);
                return editor;
            });
        };
    }

    // تصدير الدالة للاستخدام العام
    window.fixScrollbars = fixScrollbarIssues;

    // إصلاح دوري للتأكد من الاستقرار
    setInterval(fixScrollbarIssues, 5000);
});
