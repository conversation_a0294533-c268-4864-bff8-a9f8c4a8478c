/**
 * حل موحد لإصلاح مشاكل أشرطة التمرير
 * يظهر شريط التمرير الداخلي للمحرر فقط ويخفي جميع الأشرطة الأخرى
 */
document.addEventListener('DOMContentLoaded', function() {

    // دالة لإصلاح مشاكل أشرطة التمرير
    function fixScrollbarIssues() {
        // فحص إذا كان المحرر المتقدم نشطاً
        const advancedConverter = document.getElementById('advanced-converter');
        const isAdvancedActive = advancedConverter && advancedConverter.classList.contains('active');

        if (isAdvancedActive) {
            // إخفاء شريط التمرير الرئيسي للصفحة فقط في المحرر المتقدم
            document.documentElement.style.overflow = 'hidden';
            document.documentElement.style.height = '100%';
            document.body.style.overflow = 'hidden';
            document.body.style.height = '100vh';
            document.body.style.margin = '0';
            document.body.style.padding = '0';
        } else {
            // استعادة التمرير الطبيعي في التبويبات الأخرى
            document.documentElement.style.overflow = '';
            document.documentElement.style.height = '';
            document.body.style.overflow = '';
            document.body.style.height = '';
            document.body.style.margin = '';
            document.body.style.padding = '';
        }

        // إصلاح منطقة محتوى المحرر - الشريط الوحيد المسموح
        const editorContent = document.querySelector('.document-editor-content');
        if (editorContent && isAdvancedActive) {
            editorContent.style.overflowY = 'auto';
            editorContent.style.overflowX = 'hidden';
            editorContent.style.maxHeight = '500px';
            editorContent.style.height = '500px'; // ارتفاع ثابت لمنع التوسع
            editorContent.style.minHeight = '450px';
        }

        // إصلاح عناصر CKEditor - الشريط الوحيد المسموح
        const editorElements = document.querySelectorAll('.ck-editor__editable, .ck-content');
        editorElements.forEach(el => {
            if (el && isAdvancedActive) {
                el.style.overflowY = 'auto';
                el.style.overflowX = 'hidden';
                el.style.maxHeight = '500px';
                el.style.height = '500px'; // ارتفاع ثابت لمنع التوسع
                el.style.minHeight = '450px';
            }
        });

        // إصلاح شريط التمرير في المحرر البسيط
        const simpleTextareas = document.querySelectorAll('.text-area textarea, .text-area .tifinagh-text');
        simpleTextareas.forEach(el => {
            if (el) {
                el.style.overflowY = 'auto';
                el.style.scrollbarWidth = 'auto'; // Firefox
                el.style.msOverflowStyle = 'auto'; // IE
            }
        });

        // منع أشرطة التمرير في حاويات المحرر المتقدم فقط
        if (isAdvancedActive) {
            const outerContainers = document.querySelectorAll(
                '.advanced-editor-wrapper, #document-editor'
            );
            outerContainers.forEach(container => {
                if (container) {
                    container.style.overflow = 'hidden';
                    container.style.height = 'auto';
                }
            });

            // إعداد خاص للمحرر المتقدم
            if (advancedConverter) {
                advancedConverter.style.overflow = 'hidden';
                advancedConverter.style.height = 'auto';
            }
        }

        // إعداد الفوتر (فقط في المحرر المتقدم)
        const footer = document.querySelector('footer');
        if (footer && isAdvancedActive) {
            footer.style.overflow = 'hidden';
            footer.style.flexShrink = '0';
        } else if (footer && !isAdvancedActive) {
            // استعادة الإعدادات الطبيعية للفوتر
            footer.style.overflow = '';
            footer.style.flexShrink = '';
        }

        // إصلاح خاص للشاشات المختلفة (فقط في المحرر المتقدم)
        if (isAdvancedActive) {
            const responsiveElements = document.querySelectorAll('.document-editor-content, .ck-content');
            responsiveElements.forEach(el => {
                if (el) {
                    if (window.innerWidth <= 480) {
                        el.style.maxHeight = '350px';
                        el.style.height = '350px'; // ارتفاع ثابت
                        el.style.minHeight = '300px';
                    } else if (window.innerWidth <= 768) {
                        el.style.maxHeight = '400px';
                        el.style.height = '400px'; // ارتفاع ثابت
                        el.style.minHeight = '350px';
                    } else {
                        el.style.maxHeight = '500px';
                        el.style.height = '500px'; // ارتفاع ثابت
                        el.style.minHeight = '450px';
                    }
                }
            });

            // إخفاء أي أشرطة تمرير أخرى قد تظهر (فقط في المحرر المتقدم)
            const allElements = document.querySelectorAll('#advanced-converter *');
            allElements.forEach(el => {
                if (!el.matches('.document-editor-content, .ck-content, .ck-editor__editable')) {
                    el.style.scrollbarWidth = 'none'; // Firefox
                    el.style.msOverflowStyle = 'none'; // IE
                }
            });
        }
    }

    // تشغيل الإصلاح فوراً
    fixScrollbarIssues();

    // إعداد مراقب للتغييرات في DOM
    const observer = new MutationObserver(function(mutations) {
        let shouldFix = false;
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' ||
                (mutation.type === 'attributes' &&
                 ['style', 'class'].includes(mutation.attributeName))) {
                shouldFix = true;
            }
        });

        if (shouldFix) {
            setTimeout(fixScrollbarIssues, 50);
        }
    });

    // مراقبة التغييرات في الجسم
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });

    // إصلاح عند تغيير حجم النافذة
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(fixScrollbarIssues, 100);
    });

    // إصلاح عند تبديل التبويبات
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            setTimeout(fixScrollbarIssues, 150);
        });
    });

    // إصلاح عند تحميل CKEditor
    if (window.DecoupledEditor) {
        const originalCreate = window.DecoupledEditor.create;
        window.DecoupledEditor.create = function(...args) {
            return originalCreate.apply(this, args).then(editor => {
                setTimeout(fixScrollbarIssues, 200);
                return editor;
            });
        };
    }

    // تصدير الدالة للاستخدام العام
    window.fixScrollbars = fixScrollbarIssues;

    // إصلاح دوري للتأكد من الاستقرار
    setInterval(fixScrollbarIssues, 5000);
});
