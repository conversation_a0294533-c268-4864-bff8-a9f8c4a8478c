/**
 * حل موحد لإصلاح مشاكل أشرطة التمرير
 * يضمن ظهور شريط تمرير واحد فقط ويمنع التداخل
 */
document.addEventListener('DOMContentLoaded', function() {

    // دالة لإصلاح مشاكل أشرطة التمرير
    function fixScrollbarIssues() {
        // التأكد من ظهور شريط التمرير الرئيسي للصفحة
        document.documentElement.style.overflowY = 'scroll';
        document.documentElement.style.overflowX = 'hidden';
        document.body.style.overflowX = 'hidden';

        // إصلاح منطقة محتوى المحرر
        const editorContent = document.querySelector('.document-editor-content');
        if (editorContent) {
            editorContent.style.overflowY = 'auto';
            editorContent.style.overflowX = 'hidden';
            editorContent.style.maxHeight = '500px';
            editorContent.style.minHeight = '450px';
        }

        // إصلاح عناصر CKEditor
        const editorElements = document.querySelectorAll('.ck-editor__editable, .ck-content');
        editorElements.forEach(el => {
            if (el) {
                el.style.overflowY = 'auto';
                el.style.overflowX = 'hidden';
                el.style.maxHeight = '500px';
                el.style.minHeight = '450px';
            }
        });

        // منع أشرطة التمرير في الحاويات الخارجية
        const outerContainers = document.querySelectorAll(
            '.advanced-editor-wrapper, #document-editor, #advanced-converter, .tab-content, .tab-pane'
        );
        outerContainers.forEach(container => {
            if (container) {
                container.style.overflow = 'visible';
                container.style.height = 'auto';
            }
        });

        // إصلاح خاص للشاشات الصغيرة
        if (window.innerWidth <= 768) {
            const responsiveElements = document.querySelectorAll('.document-editor-content, .ck-content');
            responsiveElements.forEach(el => {
                if (el) {
                    el.style.maxHeight = window.innerWidth <= 480 ? '350px' : '400px';
                    el.style.minHeight = window.innerWidth <= 480 ? '300px' : '350px';
                }
            });
        }
    }

    // تشغيل الإصلاح فوراً
    fixScrollbarIssues();

    // إعداد مراقب للتغييرات في DOM
    const observer = new MutationObserver(function(mutations) {
        let shouldFix = false;
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' ||
                (mutation.type === 'attributes' &&
                 ['style', 'class'].includes(mutation.attributeName))) {
                shouldFix = true;
            }
        });

        if (shouldFix) {
            setTimeout(fixScrollbarIssues, 50);
        }
    });

    // مراقبة التغييرات في الجسم
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });

    // إصلاح عند تغيير حجم النافذة
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(fixScrollbarIssues, 100);
    });

    // إصلاح عند تبديل التبويبات
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            setTimeout(fixScrollbarIssues, 150);
        });
    });

    // إصلاح عند تحميل CKEditor
    if (window.DecoupledEditor) {
        const originalCreate = window.DecoupledEditor.create;
        window.DecoupledEditor.create = function(...args) {
            return originalCreate.apply(this, args).then(editor => {
                setTimeout(fixScrollbarIssues, 200);
                return editor;
            });
        };
    }

    // تصدير الدالة للاستخدام العام
    window.fixScrollbars = fixScrollbarIssues;

    // إصلاح دوري للتأكد من الاستقرار
    setInterval(fixScrollbarIssues, 5000);
});
